/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  // Transpile local packages
  transpilePackages: ['@quiz/db', '@quiz/core'],
  webpack: (config) => {
    // Handle TypeScript files in local packages
    config.module.rules.push({
      test: /\.tsx?$/,
      include: [
        /packages\/db/,
        /packages\/core/,
      ],
      use: [
        {
          loader: 'ts-loader',
          options: {
            transpileOnly: true,
            configFile: false,
            compilerOptions: {
              target: 'es5',
              lib: ['dom', 'dom.iterable', 'es6'],
              allowJs: true,
              skipLibCheck: true,
              strict: false,
              forceConsistentCasingInFileNames: true,
              noEmit: false,
              esModuleInterop: true,
              module: 'esnext',
              moduleResolution: 'node',
              resolveJsonModule: true,
              isolatedModules: true,
              jsx: 'preserve',
            },
          },
        },
      ],
    });
    return config;
  },
  images: {
    unoptimized: true,
  },
  // Configure static file serving for development
  async rewrites() {
    return [
      {
        source: '/uploads/:path*',
        destination: '/api/uploads/:path*',
      },
    ];
  },
  // Explicitly expose environment variables
  env: {
    NEXT_PUBLIC_FEATURE_ADAPTIVE_V2: process.env.NEXT_PUBLIC_FEATURE_ADAPTIVE_V2,
  },
};

module.exports = nextConfig;
