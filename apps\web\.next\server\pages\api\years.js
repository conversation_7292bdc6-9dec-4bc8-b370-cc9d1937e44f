"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/years";
exports.ids = ["pages/api/years"];
exports.modules = {

/***/ "(api-node)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fyears&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cyears.ts&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fyears&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cyears.ts&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/../../node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/../../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_years_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\years.ts */ \"(api-node)/./pages/api/years.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_years_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_years_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/years\",\n        pathname: \"/api/years\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_years_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRnllYXJzJnByZWZlcnJlZFJlZ2lvbj0mYWJzb2x1dGVQYWdlUGF0aD0uJTJGcGFnZXMlNUNhcGklNUN5ZWFycy50cyZtaWRkbGV3YXJlQ29uZmlnQmFzZTY0PWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDRTtBQUMxRDtBQUNtRDtBQUNuRDtBQUNBLGlFQUFlLHdFQUFLLENBQUMsZ0RBQVEsWUFBWSxFQUFDO0FBQzFDO0FBQ08sZUFBZSx3RUFBSyxDQUFDLGdEQUFRO0FBQ3BDO0FBQ08sd0JBQXdCLHlHQUFtQjtBQUNsRDtBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxZQUFZO0FBQ1osQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBhZ2VzQVBJUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL3BhZ2VzLWFwaS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IGhvaXN0IH0gZnJvbSBcIm5leHQvZGlzdC9idWlsZC90ZW1wbGF0ZXMvaGVscGVyc1wiO1xuLy8gSW1wb3J0IHRoZSB1c2VybGFuZCBjb2RlLlxuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIi4vcGFnZXNcXFxcYXBpXFxcXHllYXJzLnRzXCI7XG4vLyBSZS1leHBvcnQgdGhlIGhhbmRsZXIgKHNob3VsZCBiZSB0aGUgZGVmYXVsdCBleHBvcnQpLlxuZXhwb3J0IGRlZmF1bHQgaG9pc3QodXNlcmxhbmQsICdkZWZhdWx0Jyk7XG4vLyBSZS1leHBvcnQgY29uZmlnLlxuZXhwb3J0IGNvbnN0IGNvbmZpZyA9IGhvaXN0KHVzZXJsYW5kLCAnY29uZmlnJyk7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc0FQSVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFU19BUEksXG4gICAgICAgIHBhZ2U6IFwiL2FwaS95ZWFyc1wiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3llYXJzXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogJycsXG4gICAgICAgIGZpbGVuYW1lOiAnJ1xuICAgIH0sXG4gICAgdXNlcmxhbmRcbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1wYWdlcy1hcGkuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api-node)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fyears&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cyears.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/../../packages/db/index.ts":
/*!**********************************!*\
  !*** ../../packages/db/index.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _prisma_client__WEBPACK_IMPORTED_MODULE_0__) if([\"default\",\"prisma\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _prisma_client__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\nconst globalForPrisma = global;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        'error'\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prisma);\n// Re-export Prisma types for convenience\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uLi8uLi9wYWNrYWdlcy9kYi9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThDO0FBRTlDLE1BQU1DLGtCQUFrQkM7QUFFakIsTUFBTUMsU0FDWEYsZ0JBQWdCRSxNQUFNLElBQ3RCLElBQUlILHdEQUFZQSxDQUFDO0lBQ2ZJLEtBQUs7UUFBQztLQUFRO0FBQ2hCLEdBQUc7QUFFTCxJQUFJQyxJQUFxQyxFQUFFSixnQkFBZ0JFLE1BQU0sR0FBR0E7QUFFcEUsaUVBQWVBLE1BQU1BLEVBQUM7QUFFdEIseUNBQXlDO0FBQ1YiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0c1xcR2l0SHViXFxteS1xdWl6LWFwcFxccGFja2FnZXNcXGRiXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCc7XG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbCBhcyB1bmtub3duIGFzIHsgcHJpc21hOiBQcmlzbWFDbGllbnQgfTtcblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgfHxcbiAgbmV3IFByaXNtYUNsaWVudCh7XG4gICAgbG9nOiBbJ2Vycm9yJ11cbiAgfSk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hO1xuXG5leHBvcnQgZGVmYXVsdCBwcmlzbWE7XG5cbi8vIFJlLWV4cG9ydCBQcmlzbWEgdHlwZXMgZm9yIGNvbnZlbmllbmNlXG5leHBvcnQgKiBmcm9tICdAcHJpc21hL2NsaWVudCc7XG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api-node)/../../packages/db/index.ts\n");

/***/ }),

/***/ "(api-node)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _quiz_db__WEBPACK_IMPORTED_MODULE_0__.prisma)\n/* harmony export */ });\n/* harmony import */ var _quiz_db__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @quiz/db */ \"(api-node)/../../packages/db/index.ts\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL2xpYi9wcmlzbWEudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNkMiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0c1xcR2l0SHViXFxteS1xdWl6LWFwcFxcYXBwc1xcd2ViXFxsaWJcXHByaXNtYS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBwcmlzbWEgYXMgZGVmYXVsdCB9IGZyb20gJ0BxdWl6L2RiJzsiXSwibmFtZXMiOlsicHJpc21hIiwiZGVmYXVsdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api-node)/./lib/prisma.ts\n");

/***/ }),

/***/ "(api-node)/./pages/api/years.ts":
/*!****************************!*\
  !*** ./pages/api/years.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/prisma */ \"(api-node)/./lib/prisma.ts\");\n\n/**\r\n * API endpoint for fetching years\r\n * Supports:\r\n * - GET /api/years - Get all years\r\n * - GET /api/years?name=Year 5 - Get year by name\r\n * - GET /api/years?yearNumber=5 - Get year by yearNumber\r\n */ async function handler(req, res) {\n    if (req.method !== 'GET') {\n        return res.status(405).json({\n            message: 'Method Not Allowed'\n        });\n    }\n    try {\n        const { name, yearNumber } = req.query;\n        // If name is provided, extract the year number and find by yearNumber\n        if (name && typeof name === 'string') {\n            // Extract year number from the year string (e.g., \"Year 5\" -> 5)\n            const yearMatch = name.match(/\\d+/);\n            const yearNumber = yearMatch ? parseInt(yearMatch[0], 10) : null;\n            if (!yearNumber) {\n                return res.status(400).json({\n                    message: 'Invalid year name format. Expected format: \"Year X\"'\n                });\n            }\n            const year = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].year.findFirst({\n                where: {\n                    yearNumber: yearNumber\n                }\n            });\n            if (!year) {\n                return res.status(404).json({\n                    message: 'Year not found'\n                });\n            }\n            return res.status(200).json({\n                year\n            });\n        }\n        // If yearNumber is provided, find by yearNumber\n        if (yearNumber && typeof yearNumber === 'string') {\n            const year = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].year.findFirst({\n                where: {\n                    yearNumber: parseInt(yearNumber, 10)\n                }\n            });\n            if (!year) {\n                return res.status(404).json({\n                    message: 'Year not found'\n                });\n            }\n            return res.status(200).json({\n                year\n            });\n        }\n        // If no specific query parameter is provided, return all years\n        const years = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].year.findMany({\n            orderBy: {\n                yearNumber: 'asc'\n            }\n        });\n        res.status(200).json(years);\n    } catch (error) {\n        console.error('Error fetching years:', error);\n        res.status(500).json({\n            message: 'Failed to fetch years'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/years.ts\n");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fyears&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cyears.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();