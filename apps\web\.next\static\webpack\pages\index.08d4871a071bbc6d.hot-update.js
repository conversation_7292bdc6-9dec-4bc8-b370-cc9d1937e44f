"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ParentPortal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ParentPortal */ \"(pages-dir-browser)/./components/ParentPortal.tsx\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/react */ \"(pages-dir-browser)/../../node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @prisma/client */ \"(pages-dir-browser)/../../node_modules/@prisma/client/index-browser.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_5__);\n// Test component to switch between roles\n\nvar _s = $RefreshSig$();\n\n // Import useRouter\n\n\n\n// Test component to switch between roles\nfunction RoleSwitcher(param) {\n    let { onSwitch } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 bg-white p-4 rounded-lg shadow-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-sm font-bold mb-2\",\n                children: \"Test Mode: Switch Role\"\n            }, void 0, false, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onSwitch(_prisma_client__WEBPACK_IMPORTED_MODULE_5__.Role.CHILD),\n                        className: \"px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                        children: \"Student View\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onSwitch(_prisma_client__WEBPACK_IMPORTED_MODULE_5__.Role.PARENT),\n                        className: \"px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600\",\n                        children: \"Parent View\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n_c = RoleSwitcher;\nfunction Home() {\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)(); // Initialize useRouter\n    // Function to switch roles for testing\n    const switchRole = async (role)=>{\n        // Sign out current session\n        await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.signOut)({\n            redirect: false\n        });\n        // Mock credentials based on role\n        const credentials = role === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Role.CHILD ? {\n            username: 'johndoe',\n            pin: '1234'\n        } // Mock child credentials\n         : {\n            email: '<EMAIL>',\n            password: 'password123'\n        }; // Mock parent credentials\n        // Sign in with new role\n        await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.signIn)('credentials', {\n            ...credentials,\n            redirect: false\n        });\n    };\n    // Handle redirects based on authentication status and user role\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                router.push('/login');\n            } else if (status === 'authenticated' && (session === null || session === void 0 ? void 0 : session.user)) {\n                const user = session.user;\n                if (user.role === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Role.CHILD) {\n                    router.push('/student-dashboard');\n                }\n            // Parent users stay on this page to see the ParentPortal\n            }\n        }\n    }[\"Home.useEffect\"], [\n        status,\n        session,\n        router\n    ]);\n    // Show loading state while session is loading or redirecting\n    if (status === 'loading') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, this);\n    }\n    // If not authenticated, show loading while redirecting\n    if (status === 'unauthenticated') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Redirecting to login...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, this);\n    }\n    const user = session === null || session === void 0 ? void 0 : session.user;\n    // If child user, show loading while redirecting\n    if ((user === null || user === void 0 ? void 0 : user.role) === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Role.CHILD) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Redirecting to dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: (user === null || user === void 0 ? void 0 : user.role) === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Role.PARENT ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ParentPortal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            onAssignHomework: ()=>{}\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n            lineNumber: 109,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-800 mb-4\",\n                        children: \"Access Denied\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"You don't have permission to access this page.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                lineNumber: 112,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n            lineNumber: 111,\n            columnNumber: 9\n        }, this)\n    }, void 0, false);\n}\n_s(Home, \"IsB+X4/uCtap/BkD4g9WA4/8vZ8=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c1 = Home;\nvar _c, _c1;\n$RefreshReg$(_c, \"RoleSwitcher\");\n$RefreshReg$(_c1, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL3BhZ2VzL2luZGV4LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBQSx5Q0FBeUM7OztBQUNFO0FBRUgsQ0FBQyxtQkFBbUI7QUFFUDtBQUNRO0FBQ3hCO0FBRXJDLHlDQUF5QztBQUN6QyxTQUFTTyxhQUFhLEtBQWdEO1FBQWhELEVBQUVDLFFBQVEsRUFBc0MsR0FBaEQ7SUFDcEIscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDQztnQkFBR0QsV0FBVTswQkFBeUI7Ozs7OzswQkFDdkMsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0U7d0JBQ0NDLFNBQVMsSUFBTUwsU0FBU0YsZ0RBQUlBLENBQUNRLEtBQUs7d0JBQ2xDSixXQUFVO2tDQUNYOzs7Ozs7a0NBR0QsOERBQUNFO3dCQUNDQyxTQUFTLElBQU1MLFNBQVNGLGdEQUFJQSxDQUFDUyxNQUFNO3dCQUNuQ0wsV0FBVTtrQ0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTVQ7S0FwQlNIO0FBc0JNLFNBQVNTOztJQUN0QixNQUFNLEVBQUVDLE1BQU1DLE9BQU8sRUFBRUMsTUFBTSxFQUFFLEdBQUdoQiwyREFBVUE7SUFDNUMsTUFBTWlCLFNBQVNuQixzREFBU0EsSUFBSSx1QkFBdUI7SUFFbkQsdUNBQXVDO0lBQ3ZDLE1BQU1vQixhQUFhLE9BQU9DO1FBQ3hCLDJCQUEyQjtRQUMzQixNQUFNakIsd0RBQU9BLENBQUM7WUFBRWtCLFVBQVU7UUFBTTtRQUVoQyxpQ0FBaUM7UUFDakMsTUFBTUMsY0FBY0YsU0FBU2hCLGdEQUFJQSxDQUFDUSxLQUFLLEdBQ25DO1lBQUVXLFVBQVU7WUFBV0MsS0FBSztRQUFPLEVBQUcseUJBQXlCO1dBQy9EO1lBQUVDLE9BQU87WUFBc0JDLFVBQVU7UUFBYyxHQUFJLDBCQUEwQjtRQUV6Rix3QkFBd0I7UUFDeEIsTUFBTXhCLHVEQUFNQSxDQUFDLGVBQWU7WUFDMUIsR0FBR29CLFdBQVc7WUFDZEQsVUFBVTtRQUNaO0lBQ0Y7SUFFQSxnRUFBZ0U7SUFDaEV2QixnREFBU0E7MEJBQUM7WUFDUixJQUFJbUIsV0FBVyxtQkFBbUI7Z0JBQ2hDQyxPQUFPUyxJQUFJLENBQUM7WUFDZCxPQUFPLElBQUlWLFdBQVcsb0JBQW1CRCxvQkFBQUEsOEJBQUFBLFFBQVNZLElBQUksR0FBRTtnQkFDdEQsTUFBTUEsT0FBT1osUUFBUVksSUFBSTtnQkFDekIsSUFBSUEsS0FBS1IsSUFBSSxLQUFLaEIsZ0RBQUlBLENBQUNRLEtBQUssRUFBRTtvQkFDNUJNLE9BQU9TLElBQUksQ0FBQztnQkFDZDtZQUNBLHlEQUF5RDtZQUMzRDtRQUNGO3lCQUFHO1FBQUNWO1FBQVFEO1FBQVNFO0tBQU87SUFFNUIsNkRBQTZEO0lBQzdELElBQUlELFdBQVcsV0FBVztRQUN4QixxQkFDRSw4REFBQ1Y7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDcUI7d0JBQUVyQixXQUFVO2tDQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJckM7SUFFQSx1REFBdUQ7SUFDdkQsSUFBSVMsV0FBVyxtQkFBbUI7UUFDaEMscUJBQ0UsOERBQUNWO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7Ozs7OztrQ0FDZiw4REFBQ3FCO3dCQUFFckIsV0FBVTtrQ0FBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSXJDO0lBRUEsTUFBTW9CLE9BQU9aLG9CQUFBQSw4QkFBQUEsUUFBU1ksSUFBSTtJQUUxQixnREFBZ0Q7SUFDaEQsSUFBSUEsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNUixJQUFJLE1BQUtoQixnREFBSUEsQ0FBQ1EsS0FBSyxFQUFFO1FBQzdCLHFCQUNFLDhEQUFDTDtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNxQjt3QkFBRXJCLFdBQVU7a0NBQWdCOzs7Ozs7Ozs7Ozs7Ozs7OztJQUlyQztJQUVBLHFCQUNFO2tCQUVHb0IsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNUixJQUFJLE1BQUtoQixnREFBSUEsQ0FBQ1MsTUFBTSxpQkFDekIsOERBQUNiLGdFQUFZQTtZQUFDOEIsa0JBQWtCLEtBQTBDOzs7OztpQ0FFMUUsOERBQUN2QjtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUN1Qjt3QkFBR3ZCLFdBQVU7a0NBQXdDOzs7Ozs7a0NBQ3RELDhEQUFDcUI7d0JBQUVyQixXQUFVO2tDQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXpDO0dBdkZ3Qk07O1FBQ1liLHVEQUFVQTtRQUM3QkYsa0RBQVNBOzs7TUFGRmUiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0c1xcR2l0SHViXFxteS1xdWl6LWFwcFxcYXBwc1xcd2ViXFxwYWdlc1xcaW5kZXgudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRlc3QgY29tcG9uZW50IHRvIHN3aXRjaCBiZXR3ZWVuIHJvbGVzXHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcclxuaW1wb3J0IEhlYWQgZnJvbSAnbmV4dC9oZWFkJ1xyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L3JvdXRlcic7IC8vIEltcG9ydCB1c2VSb3V0ZXJcclxuaW1wb3J0IERhc2hib2FyZCBmcm9tICcuLi9jb21wb25lbnRzL0Rhc2hib2FyZCdcclxuaW1wb3J0IFBhcmVudFBvcnRhbCBmcm9tICcuLi9jb21wb25lbnRzL1BhcmVudFBvcnRhbCdcclxuaW1wb3J0IHsgdXNlU2Vzc2lvbiwgc2lnbkluLCBzaWduT3V0IH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0J1xyXG5pbXBvcnQgeyBSb2xlIH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXHJcblxyXG4vLyBUZXN0IGNvbXBvbmVudCB0byBzd2l0Y2ggYmV0d2VlbiByb2xlc1xyXG5mdW5jdGlvbiBSb2xlU3dpdGNoZXIoeyBvblN3aXRjaCB9OiB7IG9uU3dpdGNoOiAocm9sZTogUm9sZSkgPT4gdm9pZCB9KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgdG9wLTQgcmlnaHQtNCB6LTUwIGJnLXdoaXRlIHAtNCByb3VuZGVkLWxnIHNoYWRvdy1sZ1wiPlxyXG4gICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LWJvbGQgbWItMlwiPlRlc3QgTW9kZTogU3dpdGNoIFJvbGU8L2gzPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXgtMlwiPlxyXG4gICAgICAgIDxidXR0b25cclxuICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9uU3dpdGNoKFJvbGUuQ0hJTEQpfVxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0xIGJnLWJsdWUtNTAwIHRleHQtd2hpdGUgcm91bmRlZCBob3ZlcjpiZy1ibHVlLTYwMFwiXHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgU3R1ZGVudCBWaWV3XHJcbiAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgb25DbGljaz17KCkgPT4gb25Td2l0Y2goUm9sZS5QQVJFTlQpfVxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0xIGJnLWdyZWVuLTUwMCB0ZXh0LXdoaXRlIHJvdW5kZWQgaG92ZXI6YmctZ3JlZW4tNjAwXCJcclxuICAgICAgICA+XHJcbiAgICAgICAgICBQYXJlbnQgVmlld1xyXG4gICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWUoKSB7XHJcbiAgY29uc3QgeyBkYXRhOiBzZXNzaW9uLCBzdGF0dXMgfSA9IHVzZVNlc3Npb24oKTtcclxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTsgLy8gSW5pdGlhbGl6ZSB1c2VSb3V0ZXJcclxuXHJcbiAgLy8gRnVuY3Rpb24gdG8gc3dpdGNoIHJvbGVzIGZvciB0ZXN0aW5nXHJcbiAgY29uc3Qgc3dpdGNoUm9sZSA9IGFzeW5jIChyb2xlOiBSb2xlKSA9PiB7XHJcbiAgICAvLyBTaWduIG91dCBjdXJyZW50IHNlc3Npb25cclxuICAgIGF3YWl0IHNpZ25PdXQoeyByZWRpcmVjdDogZmFsc2UgfSk7XHJcblxyXG4gICAgLy8gTW9jayBjcmVkZW50aWFscyBiYXNlZCBvbiByb2xlXHJcbiAgICBjb25zdCBjcmVkZW50aWFscyA9IHJvbGUgPT09IFJvbGUuQ0hJTERcclxuICAgICAgPyB7IHVzZXJuYW1lOiAnam9obmRvZScsIHBpbjogJzEyMzQnIH0gIC8vIE1vY2sgY2hpbGQgY3JlZGVudGlhbHNcclxuICAgICAgOiB7IGVtYWlsOiAncGFyZW50QGV4YW1wbGUuY29tJywgcGFzc3dvcmQ6ICdwYXNzd29yZDEyMycgfTsgIC8vIE1vY2sgcGFyZW50IGNyZWRlbnRpYWxzXHJcblxyXG4gICAgLy8gU2lnbiBpbiB3aXRoIG5ldyByb2xlXHJcbiAgICBhd2FpdCBzaWduSW4oJ2NyZWRlbnRpYWxzJywge1xyXG4gICAgICAuLi5jcmVkZW50aWFscyxcclxuICAgICAgcmVkaXJlY3Q6IGZhbHNlXHJcbiAgICB9KTtcclxuICB9O1xyXG5cclxuICAvLyBIYW5kbGUgcmVkaXJlY3RzIGJhc2VkIG9uIGF1dGhlbnRpY2F0aW9uIHN0YXR1cyBhbmQgdXNlciByb2xlXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChzdGF0dXMgPT09ICd1bmF1dGhlbnRpY2F0ZWQnKSB7XHJcbiAgICAgIHJvdXRlci5wdXNoKCcvbG9naW4nKTtcclxuICAgIH0gZWxzZSBpZiAoc3RhdHVzID09PSAnYXV0aGVudGljYXRlZCcgJiYgc2Vzc2lvbj8udXNlcikge1xyXG4gICAgICBjb25zdCB1c2VyID0gc2Vzc2lvbi51c2VyO1xyXG4gICAgICBpZiAodXNlci5yb2xlID09PSBSb2xlLkNISUxEKSB7XHJcbiAgICAgICAgcm91dGVyLnB1c2goJy9zdHVkZW50LWRhc2hib2FyZCcpO1xyXG4gICAgICB9XHJcbiAgICAgIC8vIFBhcmVudCB1c2VycyBzdGF5IG9uIHRoaXMgcGFnZSB0byBzZWUgdGhlIFBhcmVudFBvcnRhbFxyXG4gICAgfVxyXG4gIH0sIFtzdGF0dXMsIHNlc3Npb24sIHJvdXRlcl0pO1xyXG5cclxuICAvLyBTaG93IGxvYWRpbmcgc3RhdGUgd2hpbGUgc2Vzc2lvbiBpcyBsb2FkaW5nIG9yIHJlZGlyZWN0aW5nXHJcbiAgaWYgKHN0YXR1cyA9PT0gJ2xvYWRpbmcnKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG1pbi1oLXNjcmVlbiBiZy1ncmF5LTEwMFwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTEyIHctMTIgYm9yZGVyLXQtMiBib3JkZXItYi0yIGJvcmRlci1ibHVlLTUwMCBteC1hdXRvIG1iLTRcIj48L2Rpdj5cclxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5Mb2FkaW5nLi4uPC9wPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICAvLyBJZiBub3QgYXV0aGVudGljYXRlZCwgc2hvdyBsb2FkaW5nIHdoaWxlIHJlZGlyZWN0aW5nXHJcbiAgaWYgKHN0YXR1cyA9PT0gJ3VuYXV0aGVudGljYXRlZCcpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWluLWgtc2NyZWVuIGJnLWdyYXktMTAwXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtMTIgdy0xMiBib3JkZXItdC0yIGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNTAwIG14LWF1dG8gbWItNFwiPjwvZGl2PlxyXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlJlZGlyZWN0aW5nIHRvIGxvZ2luLi4uPC9wPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICBjb25zdCB1c2VyID0gc2Vzc2lvbj8udXNlcjtcclxuXHJcbiAgLy8gSWYgY2hpbGQgdXNlciwgc2hvdyBsb2FkaW5nIHdoaWxlIHJlZGlyZWN0aW5nXHJcbiAgaWYgKHVzZXI/LnJvbGUgPT09IFJvbGUuQ0hJTEQpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWluLWgtc2NyZWVuIGJnLWdyYXktMTAwXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtMTIgdy0xMiBib3JkZXItdC0yIGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNTAwIG14LWF1dG8gbWItNFwiPjwvZGl2PlxyXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlJlZGlyZWN0aW5nIHRvIGRhc2hib2FyZC4uLjwvcD5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgIHsvKjxSb2xlU3dpdGNoZXIgb25Td2l0Y2g9e3N3aXRjaFJvbGV9IC8+Ki99XHJcbiAgICAgIHt1c2VyPy5yb2xlID09PSBSb2xlLlBBUkVOVCA/IChcclxuICAgICAgICA8UGFyZW50UG9ydGFsIG9uQXNzaWduSG9tZXdvcms9eygpID0+IHsvKiBJbXBsZW1lbnQgaG9tZXdvcmsgYXNzaWdubWVudCAqL319IC8+XHJcbiAgICAgICkgOiAoXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtaW4taC1zY3JlZW4gYmctZ3JheS0xMDBcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktODAwIG1iLTRcIj5BY2Nlc3MgRGVuaWVkPC9oMT5cclxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPllvdSBkb24ndCBoYXZlIHBlcm1pc3Npb24gdG8gYWNjZXNzIHRoaXMgcGFnZS48L3A+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgKX1cclxuICAgIDwvPlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVJvdXRlciIsIlBhcmVudFBvcnRhbCIsInVzZVNlc3Npb24iLCJzaWduSW4iLCJzaWduT3V0IiwiUm9sZSIsIlJvbGVTd2l0Y2hlciIsIm9uU3dpdGNoIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDMiLCJidXR0b24iLCJvbkNsaWNrIiwiQ0hJTEQiLCJQQVJFTlQiLCJIb21lIiwiZGF0YSIsInNlc3Npb24iLCJzdGF0dXMiLCJyb3V0ZXIiLCJzd2l0Y2hSb2xlIiwicm9sZSIsInJlZGlyZWN0IiwiY3JlZGVudGlhbHMiLCJ1c2VybmFtZSIsInBpbiIsImVtYWlsIiwicGFzc3dvcmQiLCJwdXNoIiwidXNlciIsInAiLCJvbkFzc2lnbkhvbWV3b3JrIiwiaDEiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/index.tsx\n"));

/***/ })

});