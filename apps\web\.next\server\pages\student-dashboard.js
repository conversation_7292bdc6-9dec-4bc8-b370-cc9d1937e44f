/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/student-dashboard";
exports.ids = ["pages/student-dashboard"];
exports.modules = {

/***/ "(pages-dir-node)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fstudent-dashboard&preferredRegion=&absolutePagePath=.%2Fpages%5Cstudent-dashboard.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fstudent-dashboard&preferredRegion=&absolutePagePath=.%2Fpages%5Cstudent-dashboard.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/../../node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/../../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.tsx\");\n/* harmony import */ var _pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\student-dashboard.tsx */ \"(pages-dir-node)/./pages/student-dashboard.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/student-dashboard\",\n        pathname: \"/student-dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTJnBhZ2U9JTJGc3R1ZGVudC1kYXNoYm9hcmQmcHJlZmVycmVkUmVnaW9uPSZhYnNvbHV0ZVBhZ2VQYXRoPS4lMkZwYWdlcyU1Q3N0dWRlbnQtZGFzaGJvYXJkLnRzeCZhYnNvbHV0ZUFwcFBhdGg9cHJpdmF0ZS1uZXh0LXBhZ2VzJTJGX2FwcCZhYnNvbHV0ZURvY3VtZW50UGF0aD1wcml2YXRlLW5leHQtcGFnZXMlMkZfZG9jdW1lbnQmbWlkZGxld2FyZUNvbmZpZ0Jhc2U2ND1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBd0Y7QUFDaEM7QUFDRTtBQUMxRDtBQUN5RDtBQUNWO0FBQy9DO0FBQzJEO0FBQzNEO0FBQ0EsaUVBQWUsd0VBQUssQ0FBQyx5REFBUSxZQUFZLEVBQUM7QUFDMUM7QUFDTyx1QkFBdUIsd0VBQUssQ0FBQyx5REFBUTtBQUNyQyx1QkFBdUIsd0VBQUssQ0FBQyx5REFBUTtBQUNyQywyQkFBMkIsd0VBQUssQ0FBQyx5REFBUTtBQUN6QyxlQUFlLHdFQUFLLENBQUMseURBQVE7QUFDN0Isd0JBQXdCLHdFQUFLLENBQUMseURBQVE7QUFDN0M7QUFDTyxnQ0FBZ0Msd0VBQUssQ0FBQyx5REFBUTtBQUM5QyxnQ0FBZ0Msd0VBQUssQ0FBQyx5REFBUTtBQUM5QyxpQ0FBaUMsd0VBQUssQ0FBQyx5REFBUTtBQUMvQyxnQ0FBZ0Msd0VBQUssQ0FBQyx5REFBUTtBQUM5QyxvQ0FBb0Msd0VBQUssQ0FBQyx5REFBUTtBQUN6RDtBQUNPLHdCQUF3QixrR0FBZ0I7QUFDL0M7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLGFBQWEsOERBQVc7QUFDeEIsa0JBQWtCLG1FQUFnQjtBQUNsQyxLQUFLO0FBQ0wsWUFBWTtBQUNaLENBQUM7O0FBRUQsaUMiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQYWdlc1JvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9wYWdlcy9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IGhvaXN0IH0gZnJvbSBcIm5leHQvZGlzdC9idWlsZC90ZW1wbGF0ZXMvaGVscGVyc1wiO1xuLy8gSW1wb3J0IHRoZSBhcHAgYW5kIGRvY3VtZW50IG1vZHVsZXMuXG5pbXBvcnQgKiBhcyBkb2N1bWVudCBmcm9tIFwicHJpdmF0ZS1uZXh0LXBhZ2VzL19kb2N1bWVudFwiO1xuaW1wb3J0ICogYXMgYXBwIGZyb20gXCJwcml2YXRlLW5leHQtcGFnZXMvX2FwcFwiO1xuLy8gSW1wb3J0IHRoZSB1c2VybGFuZCBjb2RlLlxuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIi4vcGFnZXNcXFxcc3R1ZGVudC1kYXNoYm9hcmQudHN4XCI7XG4vLyBSZS1leHBvcnQgdGhlIGNvbXBvbmVudCAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgJ2RlZmF1bHQnKTtcbi8vIFJlLWV4cG9ydCBtZXRob2RzLlxuZXhwb3J0IGNvbnN0IGdldFN0YXRpY1Byb3BzID0gaG9pc3QodXNlcmxhbmQsICdnZXRTdGF0aWNQcm9wcycpO1xuZXhwb3J0IGNvbnN0IGdldFN0YXRpY1BhdGhzID0gaG9pc3QodXNlcmxhbmQsICdnZXRTdGF0aWNQYXRocycpO1xuZXhwb3J0IGNvbnN0IGdldFNlcnZlclNpZGVQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCAnZ2V0U2VydmVyU2lkZVByb3BzJyk7XG5leHBvcnQgY29uc3QgY29uZmlnID0gaG9pc3QodXNlcmxhbmQsICdjb25maWcnKTtcbmV4cG9ydCBjb25zdCByZXBvcnRXZWJWaXRhbHMgPSBob2lzdCh1c2VybGFuZCwgJ3JlcG9ydFdlYlZpdGFscycpO1xuLy8gUmUtZXhwb3J0IGxlZ2FjeSBtZXRob2RzLlxuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFN0YXRpY1Byb3BzID0gaG9pc3QodXNlcmxhbmQsICd1bnN0YWJsZV9nZXRTdGF0aWNQcm9wcycpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFN0YXRpY1BhdGhzID0gaG9pc3QodXNlcmxhbmQsICd1bnN0YWJsZV9nZXRTdGF0aWNQYXRocycpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFN0YXRpY1BhcmFtcyA9IGhvaXN0KHVzZXJsYW5kLCAndW5zdGFibGVfZ2V0U3RhdGljUGFyYW1zJyk7XG5leHBvcnQgY29uc3QgdW5zdGFibGVfZ2V0U2VydmVyUHJvcHMgPSBob2lzdCh1c2VybGFuZCwgJ3Vuc3RhYmxlX2dldFNlcnZlclByb3BzJyk7XG5leHBvcnQgY29uc3QgdW5zdGFibGVfZ2V0U2VydmVyU2lkZVByb3BzID0gaG9pc3QodXNlcmxhbmQsICd1bnN0YWJsZV9nZXRTZXJ2ZXJTaWRlUHJvcHMnKTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IFBhZ2VzUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLlBBR0VTLFxuICAgICAgICBwYWdlOiBcIi9zdHVkZW50LWRhc2hib2FyZFwiLFxuICAgICAgICBwYXRobmFtZTogXCIvc3R1ZGVudC1kYXNoYm9hcmRcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnXG4gICAgfSxcbiAgICBjb21wb25lbnRzOiB7XG4gICAgICAgIC8vIGRlZmF1bHQgZXhwb3J0IG1pZ2h0IG5vdCBleGlzdCB3aGVuIG9wdGltaXplZCBmb3IgZGF0YSBvbmx5XG4gICAgICAgIEFwcDogYXBwLmRlZmF1bHQsXG4gICAgICAgIERvY3VtZW50OiBkb2N1bWVudC5kZWZhdWx0XG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fstudent-dashboard&preferredRegion=&absolutePagePath=.%2Fpages%5Cstudent-dashboard.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./components/TpPill.tsx":
/*!*******************************!*\
  !*** ./components/TpPill.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TpPill: () => (/* binding */ TpPill)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst TpPill = ({ tp, confidence })=>{\n    const colour = confidence === 'secure' ? 'bg-green-500' : confidence === 'emerging' ? 'bg-amber-500' : 'bg-gray-400';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: `${colour} text-white text-[10px] sm:text-xs px-2 py-[2px] rounded-full whitespace-nowrap`,\n        title: tp ? `Mastery TP${tp} • ${confidence}` : 'Mastery not established yet',\n        children: [\n            \"TP\\xa0\",\n            tp || '—'\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\TpPill.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL2NvbXBvbmVudHMvVHBQaWxsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBS08sTUFBTUEsU0FBUyxDQUFDLEVBQUVDLEVBQUUsRUFBRUMsVUFBVSxFQUFTO0lBQzlDLE1BQU1DLFNBQ0pELGVBQWUsV0FDWCxpQkFDQUEsZUFBZSxhQUNmLGlCQUNBO0lBRU4scUJBQ0UsOERBQUNFO1FBQ0NDLFdBQVcsR0FBR0YsT0FBTywrRUFBK0UsQ0FBQztRQUNyR0csT0FDRUwsS0FDSSxDQUFDLFVBQVUsRUFBRUEsR0FBRyxHQUFHLEVBQUVDLFlBQVksR0FDakM7O1lBRVA7WUFDVUQsTUFBTTs7Ozs7OztBQUdyQixFQUFFIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXEdpdEh1YlxcbXktcXVpei1hcHBcXGFwcHNcXHdlYlxcY29tcG9uZW50c1xcVHBQaWxsLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbnRlcmZhY2UgUHJvcHMge1xyXG4gIHRwOiBudW1iZXI7ICAgICAgICAgICAgICAgICAgICAvLyAwIHdoZW4gdW5rbm93blxyXG4gIGNvbmZpZGVuY2U6ICdzZWN1cmUnIHwgJ2VtZXJnaW5nJyB8ICdsb3cnO1xyXG59XHJcblxyXG5leHBvcnQgY29uc3QgVHBQaWxsID0gKHsgdHAsIGNvbmZpZGVuY2UgfTogUHJvcHMpID0+IHtcclxuICBjb25zdCBjb2xvdXIgPVxyXG4gICAgY29uZmlkZW5jZSA9PT0gJ3NlY3VyZSdcclxuICAgICAgPyAnYmctZ3JlZW4tNTAwJ1xyXG4gICAgICA6IGNvbmZpZGVuY2UgPT09ICdlbWVyZ2luZydcclxuICAgICAgPyAnYmctYW1iZXItNTAwJ1xyXG4gICAgICA6ICdiZy1ncmF5LTQwMCc7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8c3BhblxyXG4gICAgICBjbGFzc05hbWU9e2Ake2NvbG91cn0gdGV4dC13aGl0ZSB0ZXh0LVsxMHB4XSBzbTp0ZXh0LXhzIHB4LTIgcHktWzJweF0gcm91bmRlZC1mdWxsIHdoaXRlc3BhY2Utbm93cmFwYH1cclxuICAgICAgdGl0bGU9e1xyXG4gICAgICAgIHRwXHJcbiAgICAgICAgICA/IGBNYXN0ZXJ5IFRQJHt0cH0g4oCiICR7Y29uZmlkZW5jZX1gXHJcbiAgICAgICAgICA6ICdNYXN0ZXJ5IG5vdCBlc3RhYmxpc2hlZCB5ZXQnXHJcbiAgICAgIH1cclxuICAgID5cclxuICAgICAgVFAmbmJzcDt7dHAgfHwgJ+KAlCd9XHJcbiAgICA8L3NwYW4+XHJcbiAgKTtcclxufTtcclxuIl0sIm5hbWVzIjpbIlRwUGlsbCIsInRwIiwiY29uZmlkZW5jZSIsImNvbG91ciIsInNwYW4iLCJjbGFzc05hbWUiLCJ0aXRsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/TpPill.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./hooks/useMastery.ts":
/*!*****************************!*\
  !*** ./hooks/useMastery.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMastery: () => (/* binding */ useMastery)\n/* harmony export */ });\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! swr */ \"swr\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([swr__WEBPACK_IMPORTED_MODULE_0__]);\nswr__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nfunction useMastery(studentId, unitId) {\n    const enabled = \"on\" === 'on';\n    // Use a cache key that includes both studentId and unitId\n    const cacheKey = enabled ? `mastery-${studentId}-${unitId}` : null;\n    const { data } = (0,swr__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(cacheKey, {\n        \"useMastery.useSWR\": ()=>{\n            const url = `/api/mastery/unit/${unitId}`;\n            return fetch(url, {\n                headers: {\n                    'x-student-id': String(studentId)\n                }\n            }).then({\n                \"useMastery.useSWR\": (r)=>r.json()\n            }[\"useMastery.useSWR\"]);\n        }\n    }[\"useMastery.useSWR\"], {\n        revalidateOnFocus: false,\n        dedupingInterval: 10000\n    });\n    return data;\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./hooks/useMastery.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/DifficultyModal.tsx":
/*!***********************************!*\
  !*** ./pages/DifficultyModal.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DifficultyModal: () => (/* binding */ DifficultyModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=XIcon!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=XIcon!=!../../node_modules/lucide-react/dist/esm/lucide-react.js\");\n\n\n\nconst DifficultyModal = ({ onSelect, onClose, unit })=>{\n    const difficulties = [\n        {\n            label: 'Very Easy',\n            value: 'very-easy',\n            tpLevels: [\n                1\n            ],\n            description: 'TP1 level questions'\n        },\n        {\n            label: 'Easy',\n            value: 'easy',\n            tpLevels: [\n                2\n            ],\n            description: 'TP2 level questions'\n        },\n        {\n            label: 'Medium',\n            value: 'medium',\n            tpLevels: [\n                3,\n                4\n            ],\n            description: 'TP3-TP4 level questions'\n        },\n        {\n            label: 'Expert',\n            value: 'expert',\n            tpLevels: [\n                5,\n                6\n            ],\n            description: 'TP5-TP6 level questions'\n        },\n        {\n            label: 'Mix It',\n            value: 'mix',\n            tpLevels: [\n                1,\n                2,\n                3,\n                4,\n                5,\n                6\n            ],\n            description: 'Questions from all TP levels'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl max-w-md w-full p-6 shadow-xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-[#0D0D0D]\",\n                                    children: \"Select Difficulty\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        unit.subject,\n                                        \" - \",\n                                        unit.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"p-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_2__.XIcon, {\n                                size: 24\n                            }, void 0, false, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-4 mb-6\",\n                    children: difficulties.map((difficulty)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>onSelect(difficulty),\n                            className: \"p-4 rounded-lg font-bold text-left transition-all   hover:bg-[#04B2D9] hover:text-white   bg-white border-2 border-[#04B2D9] text-[#04B2D9]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: difficulty.label\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-normal mt-1\",\n                                        children: difficulty.description\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 15\n                            }, undefined)\n                        }, difficulty.value, false, {\n                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-center text-sm text-gray-600\",\n                    children: \"Choose a difficulty level to begin practice\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/DifficultyModal.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(pages-dir-node)/./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n// pages/_app.tsx\n\n\n\nfunction MyApp({ Component, pageProps: { session, ...pageProps } }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_2__.SessionProvider, {\n        session: session,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_app.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_app.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQSxpQkFBaUI7O0FBQ2E7QUFFbUI7QUFFakQsU0FBU0MsTUFBTSxFQUFFQyxTQUFTLEVBQUVDLFdBQVcsRUFBRUMsT0FBTyxFQUFFLEdBQUdELFdBQVcsRUFBWTtJQUMxRSxxQkFDRSw4REFBQ0gsNERBQWVBO1FBQUNJLFNBQVNBO2tCQUN4Qiw0RUFBQ0Y7WUFBVyxHQUFHQyxTQUFTOzs7Ozs7Ozs7OztBQUc5QjtBQUVBLGlFQUFlRixLQUFLQSxFQUFBIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXEdpdEh1YlxcbXktcXVpei1hcHBcXGFwcHNcXHdlYlxccGFnZXNcXF9hcHAudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhZ2VzL19hcHAudHN4XHJcbmltcG9ydCAnLi4vc3R5bGVzL2dsb2JhbHMuY3NzJ1xyXG5pbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnXHJcbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCdcclxuXHJcbmZ1bmN0aW9uIE15QXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHM6IHsgc2Vzc2lvbiwgLi4ucGFnZVByb3BzIH0gfTogQXBwUHJvcHMpIHtcclxuICByZXR1cm4gKFxyXG4gICAgPFNlc3Npb25Qcm92aWRlciBzZXNzaW9uPXtzZXNzaW9ufT5cclxuICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxyXG4gICAgPC9TZXNzaW9uUHJvdmlkZXI+XHJcbiAgKVxyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBNeUFwcFxyXG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiTXlBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJzZXNzaW9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"(pages-dir-node)/../../node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n            @font-face {\n              font-family: 'Chinese Fallback';\n              src: local('SimSun'), local('Microsoft YaHei'), local('STHeiti');\n              unicode-range: U+4E00-9FFF, U+3400-4DBF, U+20000-2A6DF, U+2A700-2B73F, U+2B740-2B81F, U+2B820-2CEAF;\n            }\n          `\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_document.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/student-dashboard.tsx":
/*!*************************************!*\
  !*** ./pages/student-dashboard.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/../../node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookIcon,ClipboardCheckIcon,FlameIcon,GemIcon,HeartIcon,HomeIcon,TrophyIcon,UserIcon!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=BookIcon,ClipboardCheckIcon,FlameIcon,GemIcon,HeartIcon,HomeIcon,TrophyIcon,UserIcon!=!../../node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_TpPill__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/TpPill */ \"(pages-dir-node)/./components/TpPill.tsx\");\n/* harmony import */ var _hooks_useMastery__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../hooks/useMastery */ \"(pages-dir-node)/./hooks/useMastery.ts\");\n/* harmony import */ var _DifficultyModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./DifficultyModal */ \"(pages-dir-node)/./pages/DifficultyModal.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_hooks_useMastery__WEBPACK_IMPORTED_MODULE_6__]);\n_hooks_useMastery__WEBPACK_IMPORTED_MODULE_6__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\n\n// Map subject names to icons and colors\nconst subjectIcons = {\n    'Math': '🔢',\n    'Science': '🧪',\n    'Chinese': '🀄',\n    'English': '📚',\n    'Malay': '🇲🇾',\n    'History': '📜',\n    'Geography': '🌍',\n    'Art': '🎨',\n    'Music': '🎵',\n    'Physical Education': '⚽'\n};\nconst subjectColors = {\n    'Math': 'bg-blue-500',\n    'Science': 'bg-green-500',\n    'Chinese': 'bg-red-500',\n    'English': 'bg-purple-500',\n    'Malay': 'bg-yellow-500',\n    'History': 'bg-amber-500',\n    'Geography': 'bg-emerald-500',\n    'Art': 'bg-pink-500',\n    'Music': 'bg-indigo-500',\n    'Physical Education': 'bg-orange-500'\n};\n// This is v2 of the dashboard.\nconst StudentDashboard = ()=>{\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [showStreakModal, setShowStreakModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [units, setUnits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [childData, setChildData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDifficultyModal, setShowDifficultyModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedUnit, setSelectedUnit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Handle error messages from query parameters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentDashboard.useEffect\": ()=>{\n            const { error: queryError } = router.query;\n            if (queryError === 'unauthorized-quiz-access') {\n                setError('You can only view your own quizzes. Please select a subject and start a new quiz.');\n                // Clear the error from URL after displaying\n                router.replace('/student-dashboard', undefined, {\n                    shallow: true\n                });\n            }\n        }\n    }[\"StudentDashboard.useEffect\"], [\n        router.query\n    ]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        streak: 5,\n        xp: 230,\n        gems: 45,\n        hearts: 5\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentDashboard.useEffect\": ()=>{\n            // Redirect to login if not authenticated\n            if (status === 'unauthenticated') {\n                router.push('/login');\n            }\n        }\n    }[\"StudentDashboard.useEffect\"], [\n        status,\n        router\n    ]);\n    // Fetch child data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentDashboard.useEffect\": ()=>{\n            if (status === 'authenticated' && session?.user) {\n                setLoading(true);\n                // Fetch the child's data\n                fetch('/api/child-data').then({\n                    \"StudentDashboard.useEffect\": (res)=>{\n                        if (!res.ok) {\n                            throw new Error('Failed to fetch child data');\n                        }\n                        return res.json();\n                    }\n                }[\"StudentDashboard.useEffect\"]).then({\n                    \"StudentDashboard.useEffect\": (data)=>{\n                        setChildData(data);\n                        // Now fetch subjects based on the child's year\n                        return fetch(`/api/subjects-by-year?yearNumber=${encodeURIComponent(data.year)}`);\n                    }\n                }[\"StudentDashboard.useEffect\"]).then({\n                    \"StudentDashboard.useEffect\": (res)=>{\n                        if (!res.ok) {\n                            throw new Error('Failed to fetch subjects');\n                        }\n                        return res.json();\n                    }\n                }[\"StudentDashboard.useEffect\"]).then({\n                    \"StudentDashboard.useEffect\": (data)=>{\n                        // Add icon and color to each subject\n                        const enhancedSubjects = data.map({\n                            \"StudentDashboard.useEffect.enhancedSubjects\": (subject)=>({\n                                    ...subject,\n                                    icon: subjectIcons[subject.name] || '📚',\n                                    color: subjectColors[subject.name] || 'bg-gray-500',\n                                    completed: 0,\n                                    total: subject.unitCount || 0,\n                                    unlocked: true\n                                })\n                        }[\"StudentDashboard.useEffect.enhancedSubjects\"]);\n                        setSubjects(enhancedSubjects);\n                        // Try to get the saved subject selection from localStorage\n                        try {\n                            const savedSubjectId = localStorage.getItem('selectedSubjectId');\n                            if (savedSubjectId !== null) {\n                                const parsedId = parseInt(savedSubjectId, 10);\n                                // Check if the saved subject exists in the fetched subjects\n                                if (!isNaN(parsedId) && enhancedSubjects.some({\n                                    \"StudentDashboard.useEffect\": (subject)=>subject.id === parsedId\n                                }[\"StudentDashboard.useEffect\"])) {\n                                    setSelectedSubject(parsedId);\n                                } else {\n                                    // If saved subject doesn't exist in current subjects, default to first one\n                                    if (enhancedSubjects.length > 0) {\n                                        setSelectedSubject(enhancedSubjects[0].id);\n                                    }\n                                }\n                            } else {\n                                // No saved selection, default to first subject\n                                if (enhancedSubjects.length > 0) {\n                                    setSelectedSubject(enhancedSubjects[0].id);\n                                }\n                            }\n                        } catch (error) {\n                            console.error('Error accessing localStorage:', error);\n                            // Fall back to default behavior\n                            if (enhancedSubjects.length > 0) {\n                                setSelectedSubject(enhancedSubjects[0].id);\n                            }\n                        }\n                        setLoading(false);\n                    }\n                }[\"StudentDashboard.useEffect\"]).catch({\n                    \"StudentDashboard.useEffect\": (error)=>{\n                        console.error('Error fetching data:', error);\n                        setError('Failed to load data. Please try again later.');\n                        setLoading(false);\n                    }\n                }[\"StudentDashboard.useEffect\"]);\n            }\n        }\n    }[\"StudentDashboard.useEffect\"], [\n        status,\n        session\n    ]);\n    // Save selected subject to localStorage when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentDashboard.useEffect\": ()=>{\n            if (selectedSubject !== null) {\n                try {\n                    localStorage.setItem('selectedSubjectId', selectedSubject.toString());\n                } catch (error) {\n                    console.error('Error saving to localStorage:', error);\n                }\n            }\n        }\n    }[\"StudentDashboard.useEffect\"], [\n        selectedSubject\n    ]);\n    // Fetch units when a subject is selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentDashboard.useEffect\": ()=>{\n            if (selectedSubject !== null && childData) {\n                setLoading(true);\n                fetch(`/api/units-by-subject-year?subjectId=${selectedSubject}&yearNumber=${encodeURIComponent(childData.year)}`).then({\n                    \"StudentDashboard.useEffect\": (res)=>{\n                        if (!res.ok) {\n                            throw new Error('Failed to fetch units');\n                        }\n                        return res.json();\n                    }\n                }[\"StudentDashboard.useEffect\"]).then({\n                    \"StudentDashboard.useEffect\": (data)=>{\n                        setUnits({\n                            \"StudentDashboard.useEffect\": (prevUnits)=>({\n                                    ...prevUnits,\n                                    [selectedSubject]: data\n                                })\n                        }[\"StudentDashboard.useEffect\"]);\n                        setLoading(false);\n                    }\n                }[\"StudentDashboard.useEffect\"]).catch({\n                    \"StudentDashboard.useEffect\": (error)=>{\n                        console.error('Error fetching units:', error);\n                        setError('Failed to load units. Please try again later.');\n                        setLoading(false);\n                    }\n                }[\"StudentDashboard.useEffect\"]);\n            }\n        }\n    }[\"StudentDashboard.useEffect\"], [\n        selectedSubject,\n        childData\n    ]);\n    // Create a component to handle mastery data for a single unit\n    const UnitMasteryPill = ({ unitId, studentId })=>{\n        const masteryData = (0,_hooks_useMastery__WEBPACK_IMPORTED_MODULE_6__.useMastery)(studentId, unitId);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TpPill__WEBPACK_IMPORTED_MODULE_5__.TpPill, {\n            tp: masteryData?.currentTp ?? 0,\n            confidence: masteryData?.confidence ?? 'low'\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n            lineNumber: 228,\n            columnNumber: 7\n        }, undefined);\n    };\n    const handleStartQuiz = (unit)=>{\n        // Show the difficulty modal and store the selected unit\n        setSelectedUnit(unit);\n        setShowDifficultyModal(true);\n    };\n    const handleDifficultySelect = async (difficulty)=>{\n        // Close the modal\n        setShowDifficultyModal(false);\n        if (!selectedUnit || !childData) return;\n        try {\n            setLoading(true);\n            // Get the year ID from the API if not available in childData\n            let yearId = childData.yearId;\n            if (!yearId) {\n                try {\n                    // Extract year number from the year string (e.g., \"Year 5\" -> 5)\n                    const yearMatch = childData.year.match(/\\d+/);\n                    const yearNumber = yearMatch ? parseInt(yearMatch[0], 10) : null;\n                    if (yearNumber) {\n                        // Fetch the year ID based on the year number\n                        const yearResponse = await fetch(`/api/years?yearNumber=${yearNumber}`);\n                        if (yearResponse.ok) {\n                            const yearData = await yearResponse.json();\n                            if (yearData && yearData.id) {\n                                yearId = yearData.id;\n                            }\n                        }\n                    }\n                } catch (error) {\n                    console.error('Error fetching year ID:', error);\n                }\n            }\n            if (!yearId) {\n                setError('Could not determine year ID. Please try again or contact support.');\n                setLoading(false);\n                return;\n            }\n            // Create a practice quiz attempt using the new API\n            const response = await fetch('/api/quiz/create-practice', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    subjectId: selectedSubject,\n                    yearId: yearId,\n                    unitId: selectedUnit.id\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to create practice quiz');\n            }\n            const data = await response.json();\n            // Check if we should use v2 quiz\n            const quizVersion = \"v2        # Controls which quiz version to use (v1 or v2)\" || 0;\n            // Very visible debugging\n            alert(`DEBUG: Quiz Version = ${quizVersion}, Env Var = ${\"v2        # Controls which quiz version to use (v1 or v2)\"}`);\n            console.log(\"Quiz version from env:\", quizVersion);\n            console.log(\"All env vars:\", {\n                NEXT_PUBLIC_QUIZ_VERSION: \"v2        # Controls which quiz version to use (v1 or v2)\",\n                NEXT_PUBLIC_FEATURE_ADAPTIVE_V2: \"on\"\n            });\n            if (quizVersion === 'v2') {\n                console.log(\"Redirecting to V2 quiz:\", `/quiz/v2/${data.attemptId}`);\n                router.push(`/quiz/v2/${data.attemptId}`);\n            } else {\n                console.log(\"Redirecting to V1 quiz:\", `/quiz?attemptId=${data.attemptId}`);\n                router.push(`/quiz?attemptId=${data.attemptId}`);\n            }\n        } catch (error) {\n            console.error('Error creating practice quiz:', error);\n            setError('Failed to start practice quiz. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDifficultyModalClose = ()=>{\n        setShowDifficultyModal(false);\n        setSelectedUnit(null);\n    };\n    const handleShowStreak = ()=>{\n        setShowStreakModal(true);\n    };\n    const handleTestClick = ()=>{\n        // Navigate to the test page or show test options\n        router.push('/start-quiz');\n    };\n    // Show loading state while checking authentication\n    if (status === 'loading' || status === 'unauthenticated') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                lineNumber: 349,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n            lineNumber: 348,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_4___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Student Dashboard | Studu\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Student dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                lineNumber: 359,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col w-full min-h-screen bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"sticky top-0 w-full bg-[#0F5FA6] text-white p-3 flex items-center justify-between shadow-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xl font-bold mr-2\",\n                                        children: \"Studu\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    childData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm bg-[#0A8CBF] px-3 py-1 rounded-full\",\n                                        children: [\n                                            childData.name,\n                                            \" | \",\n                                            childData.year\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleShowStreak,\n                                        className: \"flex items-center bg-[#0A8CBF] rounded-full px-3 py-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FlameIcon, {\n                                                size: 20,\n                                                className: \"text-[#05DBF2] mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold\",\n                                                children: stats.streak\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center bg-[#0A8CBF] rounded-full px-3 py-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.GemIcon, {\n                                                size: 20,\n                                                className: \"text-[#05DBF2] mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold\",\n                                                children: stats.gems\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center bg-[#0A8CBF] rounded-full px-3 py-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.HeartIcon, {\n                                                size: 20,\n                                                className: \"text-red-500 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold\",\n                                                children: stats.hearts\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-4 bg-gray-100 overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold mb-3\",\n                                        children: \"Subjects\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    loading && subjects.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, undefined) : error && subjects.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.location.reload(),\n                                                className: \"mt-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded text-sm\",\n                                                children: \"Retry\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, undefined) : subjects.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"No subjects found for your year. Please contact your teacher.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex overflow-x-auto pb-2 space-x-3\",\n                                        children: subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSelectedSubject(subject.id),\n                                                className: `flex flex-col items-center justify-center p-4 rounded-lg shadow-md min-w-[100px] h-[100px] ${selectedSubject === subject.id ? 'ring-4 ring-yellow-400' : ''} ${subject.unlocked ? subject.color : 'bg-gray-400'}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl mb-1\",\n                                                        children: subject.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white font-bold\",\n                                                        children: subject.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-white/80 mt-1\",\n                                                        children: [\n                                                            subject.completed,\n                                                            \"/\",\n                                                            subject.total\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, subject.id, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 11\n                            }, undefined),\n                            selectedSubject !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold mb-3\",\n                                        children: \"Learning Path\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl p-4 shadow-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl mr-3\",\n                                                        children: subjects.find((s)=>s.id === selectedSubject)?.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-bold text-lg\",\n                                                                children: subjects.find((s)=>s.id === selectedSubject)?.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"Complete lessons to unlock new content\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            loading && (!units[selectedSubject] || units[selectedSubject].length === 0) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center py-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 19\n                                            }, undefined) : error && (!units[selectedSubject] || units[selectedSubject].length === 0) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 19\n                                            }, undefined) : !units[selectedSubject] || units[selectedSubject].length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"No units found for this subject. Please contact your teacher.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col space-y-4\",\n                                                children: units[selectedSubject].map((unit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `relative border-2 rounded-lg p-4 hover:shadow-md transition-shadow ${unit.unlocked ? 'border-[#0A8CBF] bg-gradient-to-r from-[#04B2D9]/5 to-white' : 'border-gray-300 bg-gray-100'}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-bold flex items-center gap-2\",\n                                                                            children: [\n                                                                                \"Unit \",\n                                                                                unit.unitNumber || index + 1,\n                                                                                \": \",\n                                                                                unit.name,\n                                                                                 true && childData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UnitMasteryPill, {\n                                                                                    unitId: unit.id,\n                                                                                    studentId: childData.id\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                                    lineNumber: 485,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                            lineNumber: 482,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        unit.nameZh && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                            className: \"text-sm text-gray-600 mb-1\",\n                                                                            children: unit.nameZh\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                            lineNumber: 492,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                    lineNumber: 481,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                unit.unlocked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleStartQuiz({\n                                                                            id: unit.id,\n                                                                            name: unit.name,\n                                                                            subject: subjects.find((s)=>s.id === selectedSubject)?.name || ''\n                                                                        }),\n                                                                    className: \"px-4 py-2 rounded-lg font-bold bg-[#0F5FA6] text-white\",\n                                                                    children: \"Practice\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                    lineNumber: 499,\n                                                                    columnNumber: 29\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-300 p-2 rounded-full\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                        width: \"20\",\n                                                                        height: \"20\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"2\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        className: \"text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                                                width: \"18\",\n                                                                                height: \"11\",\n                                                                                x: \"3\",\n                                                                                y: \"11\",\n                                                                                rx: \"2\",\n                                                                                ry: \"2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                                lineNumber: 523,\n                                                                                columnNumber: 33\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M7 11V7a5 5 0 0 1 10 0v4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                                lineNumber: 524,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                        lineNumber: 511,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                            lineNumber: 480,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, unit.id, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"sticky bottom-0 w-full bg-white border-t border-gray-200 py-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-around items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex flex-col items-center p-2 text-[#0F5FA6]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.HomeIcon, {\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs mt-1\",\n                                            children: \"Home\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex flex-col items-center p-2 text-gray-500 hover:text-[#0A8CBF]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.BookIcon, {\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs mt-1\",\n                                            children: \"Lessons\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex flex-col items-center p-2 text-gray-500 hover:text-[#0A8CBF]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.TrophyIcon, {\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs mt-1\",\n                                            children: \"Achievements\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex flex-col items-center p-2 text-gray-500 hover:text-[#0A8CBF]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.UserIcon, {\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs mt-1\",\n                                            children: \"Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 554,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleTestClick,\n                                    className: \"flex flex-col items-center p-2 text-gray-500 hover:text-[#0A8CBF]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.ClipboardCheckIcon, {\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 562,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs mt-1\",\n                                            children: \"Test\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 558,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                            lineNumber: 541,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 540,\n                        columnNumber: 9\n                    }, undefined),\n                    showStreakModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl max-w-md w-full p-6 shadow-xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FlameIcon, {\n                                                    size: 28,\n                                                    className: \"text-[#0F5FA6] mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Your Streak\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowStreakModal(false),\n                                            className: \"p-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"2\",\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M18 6 6 18\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"m6 6 12 12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-6xl font-bold text-[#0F5FA6] mb-2\",\n                                            children: stats.streak\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                stats.streak === 1 ? 'day' : 'days',\n                                                \" in a row\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4 mb-6\",\n                                    children: [\n                                        {\n                                            days: 7,\n                                            label: '1 Week',\n                                            status: stats.streak >= 7 ? 'reached' : 'upcoming'\n                                        },\n                                        {\n                                            days: 14,\n                                            label: '2 Weeks',\n                                            status: stats.streak >= 14 ? 'reached' : 'upcoming'\n                                        },\n                                        {\n                                            days: 30,\n                                            label: '1 Month',\n                                            status: stats.streak >= 30 ? 'reached' : 'upcoming'\n                                        },\n                                        {\n                                            days: 100,\n                                            label: '100 Days',\n                                            status: stats.streak >= 100 ? 'reached' : 'upcoming'\n                                        }\n                                    ].map((milestone)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `flex justify-between items-center p-4 rounded-lg ${milestone.status === 'reached' ? 'bg-[#04B2D9]/10' : 'bg-gray-100'}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `w-8 h-8 rounded-full flex items-center justify-center mr-3 ${milestone.status === 'reached' ? 'bg-[#0F5FA6] text-white' : 'bg-gray-300'}`,\n                                                            children: milestone.status === 'reached' ? '✓' : ''\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                            lineNumber: 614,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: milestone.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-bold\",\n                                                    children: [\n                                                        milestone.days,\n                                                        \" days\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                    lineNumber: 623,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, milestone.days, true, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 607,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 600,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center text-gray-600 text-sm\",\n                                    children: \"Keep learning daily to build your streak!\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 627,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                            lineNumber: 571,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 570,\n                        columnNumber: 11\n                    }, undefined),\n                    showDifficultyModal && selectedUnit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DifficultyModal__WEBPACK_IMPORTED_MODULE_7__.DifficultyModal, {\n                        onSelect: handleDifficultySelect,\n                        onClose: handleDifficultyModalClose,\n                        unit: selectedUnit\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 636,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                lineNumber: 364,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StudentDashboard);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/student-dashboard.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=BookIcon,ClipboardCheckIcon,FlameIcon,GemIcon,HeartIcon,HomeIcon,TrophyIcon,UserIcon!=!../../node_modules/lucide-react/dist/esm/lucide-react.js":
/*!*********************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BookIcon,ClipboardCheckIcon,FlameIcon,GemIcon,HeartIcon,HomeIcon,TrophyIcon,UserIcon!=!../../node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \*********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BookIcon: () => (/* reexport safe */ _icons_book_js__WEBPACK_IMPORTED_MODULE_0__["default"]),
/* harmony export */   ClipboardCheckIcon: () => (/* reexport safe */ _icons_clipboard_check_js__WEBPACK_IMPORTED_MODULE_1__["default"]),
/* harmony export */   FlameIcon: () => (/* reexport safe */ _icons_flame_js__WEBPACK_IMPORTED_MODULE_2__["default"]),
/* harmony export */   GemIcon: () => (/* reexport safe */ _icons_gem_js__WEBPACK_IMPORTED_MODULE_3__["default"]),
/* harmony export */   HeartIcon: () => (/* reexport safe */ _icons_heart_js__WEBPACK_IMPORTED_MODULE_4__["default"]),
/* harmony export */   HomeIcon: () => (/* reexport safe */ _icons_house_js__WEBPACK_IMPORTED_MODULE_5__["default"]),
/* harmony export */   TrophyIcon: () => (/* reexport safe */ _icons_trophy_js__WEBPACK_IMPORTED_MODULE_6__["default"]),
/* harmony export */   UserIcon: () => (/* reexport safe */ _icons_user_js__WEBPACK_IMPORTED_MODULE_7__["default"])
/* harmony export */ });
/* harmony import */ var _icons_book_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/book.js */ "(pages-dir-node)/../../node_modules/lucide-react/dist/esm/icons/book.js");
/* harmony import */ var _icons_clipboard_check_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/clipboard-check.js */ "(pages-dir-node)/../../node_modules/lucide-react/dist/esm/icons/clipboard-check.js");
/* harmony import */ var _icons_flame_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/flame.js */ "(pages-dir-node)/../../node_modules/lucide-react/dist/esm/icons/flame.js");
/* harmony import */ var _icons_gem_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/gem.js */ "(pages-dir-node)/../../node_modules/lucide-react/dist/esm/icons/gem.js");
/* harmony import */ var _icons_heart_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/heart.js */ "(pages-dir-node)/../../node_modules/lucide-react/dist/esm/icons/heart.js");
/* harmony import */ var _icons_house_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/house.js */ "(pages-dir-node)/../../node_modules/lucide-react/dist/esm/icons/house.js");
/* harmony import */ var _icons_trophy_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/trophy.js */ "(pages-dir-node)/../../node_modules/lucide-react/dist/esm/icons/trophy.js");
/* harmony import */ var _icons_user_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/user.js */ "(pages-dir-node)/../../node_modules/lucide-react/dist/esm/icons/user.js");










/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=XIcon!=!../../node_modules/lucide-react/dist/esm/lucide-react.js":
/*!**************************************************************************************************!*\
  !*** __barrel_optimize__?names=XIcon!=!../../node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   XIcon: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_0__["default"])
/* harmony export */ });
/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/x.js */ "(pages-dir-node)/../../node_modules/lucide-react/dist/esm/icons/x.js");



/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next-auth/react":
/*!**********************************!*\
  !*** external "next-auth/react" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-auth/react");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "swr":
/*!**********************!*\
  !*** external "swr" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = import("swr");;

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(pages-dir-node)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fstudent-dashboard&preferredRegion=&absolutePagePath=.%2Fpages%5Cstudent-dashboard.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();