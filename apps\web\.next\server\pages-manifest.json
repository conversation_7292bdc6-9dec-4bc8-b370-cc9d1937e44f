{"/_app": "pages/_app.js", "/_error": "pages/_error.js", "/_document": "pages/_document.js", "/api/child-data": "pages/api/child-data.js", "/api/auth/[...nextauth]": "pages/api/auth/[...nextauth].js", "/student-dashboard": "pages/student-dashboard.js", "/api/subjects-by-year": "pages/api/subjects-by-year.js", "/api/units-by-subject-year": "pages/api/units-by-subject-year.js", "/api/mastery/unit/[unitId]": "pages/api/mastery/unit/[unitId].js", "/": "pages/index.js", "/login": "pages/login.js", "/api/admin/accounts": "pages/api/admin/accounts.js", "/api/admin/curriculum": "pages/api/admin/curriculum.js", "/admin": "pages/admin.js", "/api/quiz/create-practice": "pages/api/quiz/create-practice.js", "/quiz/[attemptId]": "pages/quiz/[attemptId].js", "/quiz": "pages/quiz.js", "/api/quiz/[attemptId]": "pages/api/quiz/[attemptId].js", "/api/quiz-type": "pages/api/quiz-type.js", "/api/admin/batches": "pages/api/admin/batches.js", "/api/years": "pages/api/years.js", "/api/topics": "pages/api/topics.js", "/api/subjects": "pages/api/subjects.js"}