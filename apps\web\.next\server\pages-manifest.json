{"/_app": "pages/_app.js", "/_error": "pages/_error.js", "/_document": "pages/_document.js", "/student-dashboard": "pages/student-dashboard.js", "/quiz/[attemptId]": "pages/quiz/[attemptId].js", "/api/auth/[...nextauth]": "pages/api/auth/[...nextauth].js", "/api/child-data": "pages/api/child-data.js", "/api/subjects-by-year": "pages/api/subjects-by-year.js", "/api/units-by-subject-year": "pages/api/units-by-subject-year.js", "/api/mastery/unit/[unitId]": "pages/api/mastery/unit/[unitId].js", "/quiz/v2/[attemptId]": "pages/quiz/v2/[attemptId].js", "/api/log-quiz-attempt": "pages/api/log-quiz-attempt.js", "/api/quiz/create-practice": "pages/api/quiz/create-practice.js", "/quiz": "pages/quiz.js", "/api/quiz/[attemptId]": "pages/api/quiz/[attemptId].js", "/api/quiz-type": "pages/api/quiz-type.js"}