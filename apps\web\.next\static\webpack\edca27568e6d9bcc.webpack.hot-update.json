{"c": ["webpack"], "r": ["pages/login", "pages/admin"], "m": ["(pages-dir-browser)/../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cprojects%5CGitHub%5Cmy-quiz-app%5Capps%5Cweb%5Cpages%5Clogin.tsx&page=%2Flogin!", "(pages-dir-browser)/../../node_modules/next/dist/client/get-domain-locale.js", "(pages-dir-browser)/../../node_modules/next/dist/client/link.js", "(pages-dir-browser)/../../node_modules/next/dist/client/use-intersection.js", "(pages-dir-browser)/../../node_modules/next/dist/client/use-merged-ref.js", "(pages-dir-browser)/../../node_modules/next/dist/shared/lib/utils/error-once.js", "(pages-dir-browser)/../../node_modules/next/link.js", "(pages-dir-browser)/./pages/login.tsx", "(pages-dir-browser)/../../node_modules/@ungap/structured-clone/esm/deserialize.js", "(pages-dir-browser)/../../node_modules/@ungap/structured-clone/esm/index.js", "(pages-dir-browser)/../../node_modules/@ungap/structured-clone/esm/serialize.js", "(pages-dir-browser)/../../node_modules/@ungap/structured-clone/esm/types.js", "(pages-dir-browser)/../../node_modules/bail/index.js", "(pages-dir-browser)/../../node_modules/comma-separated-tokens/index.js", "(pages-dir-browser)/../../node_modules/debug/src/browser.js", "(pages-dir-browser)/../../node_modules/debug/src/common.js", "(pages-dir-browser)/../../node_modules/decode-named-character-reference/index.dom.js", "(pages-dir-browser)/../../node_modules/dequal/dist/index.mjs", "(pages-dir-browser)/../../node_modules/devlop/lib/development.js", "(pages-dir-browser)/../../node_modules/estree-util-is-identifier-name/index.js", "(pages-dir-browser)/../../node_modules/estree-util-is-identifier-name/lib/index.js", "(pages-dir-browser)/../../node_modules/extend/index.js", "(pages-dir-browser)/../../node_modules/hast-util-to-jsx-runtime/index.js", "(pages-dir-browser)/../../node_modules/hast-util-to-jsx-runtime/lib/index.js", "(pages-dir-browser)/../../node_modules/hast-util-whitespace/index.js", "(pages-dir-browser)/../../node_modules/hast-util-whitespace/lib/index.js", "(pages-dir-browser)/../../node_modules/html-url-attributes/index.js", "(pages-dir-browser)/../../node_modules/html-url-attributes/lib/index.js", "(pages-dir-browser)/../../node_modules/inline-style-parser/index.js", "(pages-dir-browser)/../../node_modules/is-plain-obj/index.js", "(pages-dir-browser)/../../node_modules/mdast-util-from-markdown/dev/index.js", "(pages-dir-browser)/../../node_modules/mdast-util-from-markdown/dev/lib/index.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/index.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/footer.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/handlers/blockquote.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/handlers/break.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/handlers/code.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/handlers/delete.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/handlers/emphasis.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/handlers/heading.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/handlers/html.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/handlers/image-reference.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/handlers/image.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/handlers/index.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/handlers/inline-code.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/handlers/link-reference.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/handlers/link.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/handlers/list-item.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/handlers/list.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/handlers/paragraph.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/handlers/root.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/handlers/strong.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/handlers/table-cell.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/handlers/table-row.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/handlers/table.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/handlers/text.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/index.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/revert.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-hast/lib/state.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-string/index.js", "(pages-dir-browser)/../../node_modules/mdast-util-to-string/lib/index.js", "(pages-dir-browser)/../../node_modules/micromark-core-commonmark/dev/index.js", "(pages-dir-browser)/../../node_modules/micromark-core-commonmark/dev/lib/attention.js", "(pages-dir-browser)/../../node_modules/micromark-core-commonmark/dev/lib/autolink.js", "(pages-dir-browser)/../../node_modules/micromark-core-commonmark/dev/lib/blank-line.js", "(pages-dir-browser)/../../node_modules/micromark-core-commonmark/dev/lib/block-quote.js", "(pages-dir-browser)/../../node_modules/micromark-core-commonmark/dev/lib/character-escape.js", "(pages-dir-browser)/../../node_modules/micromark-core-commonmark/dev/lib/character-reference.js", "(pages-dir-browser)/../../node_modules/micromark-core-commonmark/dev/lib/code-fenced.js", "(pages-dir-browser)/../../node_modules/micromark-core-commonmark/dev/lib/code-indented.js", "(pages-dir-browser)/../../node_modules/micromark-core-commonmark/dev/lib/code-text.js", "(pages-dir-browser)/../../node_modules/micromark-core-commonmark/dev/lib/content.js", "(pages-dir-browser)/../../node_modules/micromark-core-commonmark/dev/lib/definition.js", "(pages-dir-browser)/../../node_modules/micromark-core-commonmark/dev/lib/hard-break-escape.js", "(pages-dir-browser)/../../node_modules/micromark-core-commonmark/dev/lib/heading-atx.js", "(pages-dir-browser)/../../node_modules/micromark-core-commonmark/dev/lib/html-flow.js", "(pages-dir-browser)/../../node_modules/micromark-core-commonmark/dev/lib/html-text.js", "(pages-dir-browser)/../../node_modules/micromark-core-commonmark/dev/lib/label-end.js", "(pages-dir-browser)/../../node_modules/micromark-core-commonmark/dev/lib/label-start-image.js", "(pages-dir-browser)/../../node_modules/micromark-core-commonmark/dev/lib/label-start-link.js", "(pages-dir-browser)/../../node_modules/micromark-core-commonmark/dev/lib/line-ending.js", "(pages-dir-browser)/../../node_modules/micromark-core-commonmark/dev/lib/list.js", "(pages-dir-browser)/../../node_modules/micromark-core-commonmark/dev/lib/setext-underline.js", "(pages-dir-browser)/../../node_modules/micromark-core-commonmark/dev/lib/thematic-break.js", "(pages-dir-browser)/../../node_modules/micromark-factory-destination/dev/index.js", "(pages-dir-browser)/../../node_modules/micromark-factory-label/dev/index.js", "(pages-dir-browser)/../../node_modules/micromark-factory-space/dev/index.js", "(pages-dir-browser)/../../node_modules/micromark-factory-title/dev/index.js", "(pages-dir-browser)/../../node_modules/micromark-factory-whitespace/dev/index.js", "(pages-dir-browser)/../../node_modules/micromark-util-character/dev/index.js", "(pages-dir-browser)/../../node_modules/micromark-util-chunked/dev/index.js", "(pages-dir-browser)/../../node_modules/micromark-util-classify-character/dev/index.js", "(pages-dir-browser)/../../node_modules/micromark-util-combine-extensions/index.js", "(pages-dir-browser)/../../node_modules/micromark-util-decode-numeric-character-reference/dev/index.js", "(pages-dir-browser)/../../node_modules/micromark-util-decode-string/dev/index.js", "(pages-dir-browser)/../../node_modules/micromark-util-encode/index.js", "(pages-dir-browser)/../../node_modules/micromark-util-html-tag-name/index.js", "(pages-dir-browser)/../../node_modules/micromark-util-normalize-identifier/dev/index.js", "(pages-dir-browser)/../../node_modules/micromark-util-resolve-all/index.js", "(pages-dir-browser)/../../node_modules/micromark-util-sanitize-uri/dev/index.js", "(pages-dir-browser)/../../node_modules/micromark-util-subtokenize/dev/index.js", "(pages-dir-browser)/../../node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js", "(pages-dir-browser)/../../node_modules/micromark-util-symbol/lib/codes.js", "(pages-dir-browser)/../../node_modules/micromark-util-symbol/lib/constants.js", "(pages-dir-browser)/../../node_modules/micromark-util-symbol/lib/default.js", "(pages-dir-browser)/../../node_modules/micromark-util-symbol/lib/types.js", "(pages-dir-browser)/../../node_modules/micromark-util-symbol/lib/values.js", "(pages-dir-browser)/../../node_modules/micromark/dev/index.js", "(pages-dir-browser)/../../node_modules/micromark/dev/lib/compile.js", "(pages-dir-browser)/../../node_modules/micromark/dev/lib/constructs.js", "(pages-dir-browser)/../../node_modules/micromark/dev/lib/create-tokenizer.js", "(pages-dir-browser)/../../node_modules/micromark/dev/lib/initialize/content.js", "(pages-dir-browser)/../../node_modules/micromark/dev/lib/initialize/document.js", "(pages-dir-browser)/../../node_modules/micromark/dev/lib/initialize/flow.js", "(pages-dir-browser)/../../node_modules/micromark/dev/lib/initialize/text.js", "(pages-dir-browser)/../../node_modules/micromark/dev/lib/parse.js", "(pages-dir-browser)/../../node_modules/micromark/dev/lib/postprocess.js", "(pages-dir-browser)/../../node_modules/micromark/dev/lib/preprocess.js", "(pages-dir-browser)/../../node_modules/ms/index.js", "(pages-dir-browser)/../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cprojects%5CGitHub%5Cmy-quiz-app%5Capps%5Cweb%5Cpages%5Cadmin%5Cindex.tsx&page=%2Fadmin!", "(pages-dir-browser)/../../node_modules/property-information/index.js", "(pages-dir-browser)/../../node_modules/property-information/lib/aria.js", "(pages-dir-browser)/../../node_modules/property-information/lib/find.js", "(pages-dir-browser)/../../node_modules/property-information/lib/hast-to-react.js", "(pages-dir-browser)/../../node_modules/property-information/lib/html.js", "(pages-dir-browser)/../../node_modules/property-information/lib/normalize.js", "(pages-dir-browser)/../../node_modules/property-information/lib/svg.js", "(pages-dir-browser)/../../node_modules/property-information/lib/util/case-insensitive-transform.js", "(pages-dir-browser)/../../node_modules/property-information/lib/util/case-sensitive-transform.js", "(pages-dir-browser)/../../node_modules/property-information/lib/util/create.js", "(pages-dir-browser)/../../node_modules/property-information/lib/util/defined-info.js", "(pages-dir-browser)/../../node_modules/property-information/lib/util/info.js", "(pages-dir-browser)/../../node_modules/property-information/lib/util/merge.js", "(pages-dir-browser)/../../node_modules/property-information/lib/util/schema.js", "(pages-dir-browser)/../../node_modules/property-information/lib/util/types.js", "(pages-dir-browser)/../../node_modules/property-information/lib/xlink.js", "(pages-dir-browser)/../../node_modules/property-information/lib/xml.js", "(pages-dir-browser)/../../node_modules/property-information/lib/xmlns.js", "(pages-dir-browser)/../../node_modules/react-highlight-words/dist/main.js", "(pages-dir-browser)/../../node_modules/react-markdown/index.js", "(pages-dir-browser)/../../node_modules/react-markdown/lib/index.js", "(pages-dir-browser)/../../node_modules/remark-parse/index.js", "(pages-dir-browser)/../../node_modules/remark-parse/lib/index.js", "(pages-dir-browser)/../../node_modules/remark-rehype/index.js", "(pages-dir-browser)/../../node_modules/remark-rehype/lib/index.js", "(pages-dir-browser)/../../node_modules/space-separated-tokens/index.js", "(pages-dir-browser)/../../node_modules/style-to-js/cjs/index.js", "(pages-dir-browser)/../../node_modules/style-to-js/cjs/utilities.js", "(pages-dir-browser)/../../node_modules/style-to-object/cjs/index.js", "(pages-dir-browser)/../../node_modules/trim-lines/index.js", "(pages-dir-browser)/../../node_modules/trough/index.js", "(pages-dir-browser)/../../node_modules/trough/lib/index.js", "(pages-dir-browser)/../../node_modules/unified/index.js", "(pages-dir-browser)/../../node_modules/unified/lib/callable-instance.js", "(pages-dir-browser)/../../node_modules/unified/lib/index.js", "(pages-dir-browser)/../../node_modules/unist-util-is/index.js", "(pages-dir-browser)/../../node_modules/unist-util-is/lib/index.js", "(pages-dir-browser)/../../node_modules/unist-util-position/index.js", "(pages-dir-browser)/../../node_modules/unist-util-position/lib/index.js", "(pages-dir-browser)/../../node_modules/unist-util-stringify-position/index.js", "(pages-dir-browser)/../../node_modules/unist-util-stringify-position/lib/index.js", "(pages-dir-browser)/../../node_modules/unist-util-visit-parents/index.js", "(pages-dir-browser)/../../node_modules/unist-util-visit-parents/lib/color.js", "(pages-dir-browser)/../../node_modules/unist-util-visit-parents/lib/index.js", "(pages-dir-browser)/../../node_modules/unist-util-visit/index.js", "(pages-dir-browser)/../../node_modules/unist-util-visit/lib/index.js", "(pages-dir-browser)/../../node_modules/uuid/dist/esm-browser/index.js", "(pages-dir-browser)/../../node_modules/uuid/dist/esm-browser/max.js", "(pages-dir-browser)/../../node_modules/uuid/dist/esm-browser/md5.js", "(pages-dir-browser)/../../node_modules/uuid/dist/esm-browser/native.js", "(pages-dir-browser)/../../node_modules/uuid/dist/esm-browser/nil.js", "(pages-dir-browser)/../../node_modules/uuid/dist/esm-browser/parse.js", "(pages-dir-browser)/../../node_modules/uuid/dist/esm-browser/regex.js", "(pages-dir-browser)/../../node_modules/uuid/dist/esm-browser/rng.js", "(pages-dir-browser)/../../node_modules/uuid/dist/esm-browser/sha1.js", "(pages-dir-browser)/../../node_modules/uuid/dist/esm-browser/stringify.js", "(pages-dir-browser)/../../node_modules/uuid/dist/esm-browser/v1.js", "(pages-dir-browser)/../../node_modules/uuid/dist/esm-browser/v1ToV6.js", "(pages-dir-browser)/../../node_modules/uuid/dist/esm-browser/v3.js", "(pages-dir-browser)/../../node_modules/uuid/dist/esm-browser/v35.js", "(pages-dir-browser)/../../node_modules/uuid/dist/esm-browser/v4.js", "(pages-dir-browser)/../../node_modules/uuid/dist/esm-browser/v5.js", "(pages-dir-browser)/../../node_modules/uuid/dist/esm-browser/v6.js", "(pages-dir-browser)/../../node_modules/uuid/dist/esm-browser/v6ToV1.js", "(pages-dir-browser)/../../node_modules/uuid/dist/esm-browser/v7.js", "(pages-dir-browser)/../../node_modules/uuid/dist/esm-browser/validate.js", "(pages-dir-browser)/../../node_modules/uuid/dist/esm-browser/version.js", "(pages-dir-browser)/../../node_modules/vfile-message/index.js", "(pages-dir-browser)/../../node_modules/vfile-message/lib/index.js", "(pages-dir-browser)/../../node_modules/vfile/index.js", "(pages-dir-browser)/../../node_modules/vfile/lib/index.js", "(pages-dir-browser)/../../node_modules/vfile/lib/minpath.browser.js", "(pages-dir-browser)/../../node_modules/vfile/lib/minproc.browser.js", "(pages-dir-browser)/../../node_modules/vfile/lib/minurl.browser.js", "(pages-dir-browser)/../../node_modules/vfile/lib/minurl.shared.js", "(pages-dir-browser)/./components/AdminDashboard.tsx", "(pages-dir-browser)/./components/admin/BatchesTable.tsx", "(pages-dir-browser)/./components/admin/ChildConfigModal.tsx", "(pages-dir-browser)/./components/admin/NotesUploader.tsx", "(pages-dir-browser)/./components/admin/QuestionGenerator.tsx", "(pages-dir-browser)/./components/admin/QuestionModal.tsx", "(pages-dir-browser)/./components/admin/ResetPinModal.tsx", "(pages-dir-browser)/./components/admin/TokenUsageSummary.tsx", "(pages-dir-browser)/./components/quizV2/AiTutorAvatar.tsx", "(pages-dir-browser)/./components/quizV2/AiTutorNotification.tsx", "(pages-dir-browser)/./components/quizV2/AiTutorPanel.tsx", "(pages-dir-browser)/./components/quizV2/AnswerPanel.tsx", "(pages-dir-browser)/./components/quizV2/FlagButton.tsx", "(pages-dir-browser)/./components/quizV2/HintFab.tsx", "(pages-dir-browser)/./components/quizV2/HintSheet.tsx", "(pages-dir-browser)/./components/quizV2/LanguageToggle.tsx", "(pages-dir-browser)/./components/quizV2/Layout.tsx", "(pages-dir-browser)/./components/quizV2/NextButtonBar.tsx", "(pages-dir-browser)/./components/quizV2/ProgressBar.tsx", "(pages-dir-browser)/./components/quizV2/QuestionCounter.tsx", "(pages-dir-browser)/./components/quizV2/QuestionPanel.tsx", "(pages-dir-browser)/./components/quizV2/QuizV2Provider.tsx", "(pages-dir-browser)/./components/quizV2/index.ts", "(pages-dir-browser)/./components/renderers/HighlightedText.tsx", "(pages-dir-browser)/./hooks/useAnswerSubmit.ts", "(pages-dir-browser)/./hooks/useLocalStorage.ts", "(pages-dir-browser)/./hooks/useTranslateBubble.ts", "(pages-dir-browser)/./hooks/useTranslateV2.ts", "(pages-dir-browser)/./pages/admin/index.tsx", "(pages-dir-browser)/./pages/admin/questions.tsx"]}