"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/admin/curriculum";
exports.ids = ["pages/api/admin/curriculum"];
exports.modules = {

/***/ "(api-node)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fcurriculum&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Ccurriculum.ts&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fcurriculum&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Ccurriculum.ts&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/../../node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/../../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_admin_curriculum_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\admin\\curriculum.ts */ \"(api-node)/./pages/api/admin/curriculum.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_curriculum_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_curriculum_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/admin/curriculum\",\n        pathname: \"/api/admin/curriculum\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_admin_curriculum_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fcurriculum&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Ccurriculum.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/../../packages/db/index.ts":
/*!**********************************!*\
  !*** ../../packages/db/index.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _prisma_client__WEBPACK_IMPORTED_MODULE_0__) if([\"default\",\"prisma\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _prisma_client__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\nconst globalForPrisma = global;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        'error'\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prisma);\n// Re-export Prisma types for convenience\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uLi8uLi9wYWNrYWdlcy9kYi9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThDO0FBRTlDLE1BQU1DLGtCQUFrQkM7QUFFakIsTUFBTUMsU0FDWEYsZ0JBQWdCRSxNQUFNLElBQ3RCLElBQUlILHdEQUFZQSxDQUFDO0lBQ2ZJLEtBQUs7UUFBQztLQUFRO0FBQ2hCLEdBQUc7QUFFTCxJQUFJQyxJQUFxQyxFQUFFSixnQkFBZ0JFLE1BQU0sR0FBR0E7QUFFcEUsaUVBQWVBLE1BQU1BLEVBQUM7QUFFdEIseUNBQXlDO0FBQ1YiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0c1xcR2l0SHViXFxteS1xdWl6LWFwcFxccGFja2FnZXNcXGRiXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCc7XG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbCBhcyB1bmtub3duIGFzIHsgcHJpc21hOiBQcmlzbWFDbGllbnQgfTtcblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgfHxcbiAgbmV3IFByaXNtYUNsaWVudCh7XG4gICAgbG9nOiBbJ2Vycm9yJ11cbiAgfSk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hO1xuXG5leHBvcnQgZGVmYXVsdCBwcmlzbWE7XG5cbi8vIFJlLWV4cG9ydCBQcmlzbWEgdHlwZXMgZm9yIGNvbnZlbmllbmNlXG5leHBvcnQgKiBmcm9tICdAcHJpc21hL2NsaWVudCc7XG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api-node)/../../packages/db/index.ts\n");

/***/ }),

/***/ "(api-node)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _quiz_db__WEBPACK_IMPORTED_MODULE_0__.prisma)\n/* harmony export */ });\n/* harmony import */ var _quiz_db__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @quiz/db */ \"(api-node)/../../packages/db/index.ts\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL2xpYi9wcmlzbWEudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNkMiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0c1xcR2l0SHViXFxteS1xdWl6LWFwcFxcYXBwc1xcd2ViXFxsaWJcXHByaXNtYS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBwcmlzbWEgYXMgZGVmYXVsdCB9IGZyb20gJ0BxdWl6L2RiJzsiXSwibmFtZXMiOlsicHJpc21hIiwiZGVmYXVsdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api-node)/./lib/prisma.ts\n");

/***/ }),

/***/ "(api-node)/./pages/api/admin/curriculum.ts":
/*!***************************************!*\
  !*** ./pages/api/admin/curriculum.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/prisma */ \"(api-node)/./lib/prisma.ts\");\n\nasync function handler(req, res) {\n    if (req.method !== 'GET') {\n        return res.status(405).json({\n            message: 'Method not allowed'\n        });\n    }\n    try {\n        const years = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].year.findMany({\n            include: {\n                units: {\n                    include: {\n                        subject: true\n                    }\n                }\n            }\n        });\n        // Restructure the data to match the frontend's expected format\n        const structuredData = years.map((year)=>{\n            const subjectMap = new Map();\n            // Group units by subject\n            year.units.forEach((unit)=>{\n                if (!subjectMap.has(unit.subject.id)) {\n                    subjectMap.set(unit.subject.id, {\n                        id: unit.subject.id,\n                        name: unit.subject.name,\n                        units: []\n                    });\n                }\n                subjectMap.get(unit.subject.id).units.push({\n                    id: unit.id,\n                    unitNumber: unit.unitNumber,\n                    topicEn: unit.topicEn,\n                    topicZh: unit.topicZh\n                });\n            });\n            return {\n                id: year.id,\n                yearNumber: year.yearNumber,\n                subjects: Array.from(subjectMap.values())\n            };\n        });\n        res.status(200).json(structuredData);\n    } catch (error) {\n        console.error('Error fetching curriculum:', error);\n        res.status(500).json({\n            message: 'Error fetching curriculum data'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/admin/curriculum.ts\n");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fcurriculum&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Ccurriculum.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();