{"c": ["webpack"], "r": ["pages/student-dashboard"], "m": ["(pages-dir-browser)/../../node_modules/lucide-react/dist/esm/Icon.js", "(pages-dir-browser)/../../node_modules/lucide-react/dist/esm/createLucideIcon.js", "(pages-dir-browser)/../../node_modules/lucide-react/dist/esm/defaultAttributes.js", "(pages-dir-browser)/../../node_modules/lucide-react/dist/esm/icons/book.js", "(pages-dir-browser)/../../node_modules/lucide-react/dist/esm/icons/clipboard-check.js", "(pages-dir-browser)/../../node_modules/lucide-react/dist/esm/icons/flame.js", "(pages-dir-browser)/../../node_modules/lucide-react/dist/esm/icons/gem.js", "(pages-dir-browser)/../../node_modules/lucide-react/dist/esm/icons/heart.js", "(pages-dir-browser)/../../node_modules/lucide-react/dist/esm/icons/house.js", "(pages-dir-browser)/../../node_modules/lucide-react/dist/esm/icons/trophy.js", "(pages-dir-browser)/../../node_modules/lucide-react/dist/esm/icons/user.js", "(pages-dir-browser)/../../node_modules/lucide-react/dist/esm/icons/x.js", "(pages-dir-browser)/../../node_modules/lucide-react/dist/esm/shared/src/utils.js", "(pages-dir-browser)/../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cprojects%5CGitHub%5Cmy-quiz-app%5Capps%5Cweb%5Cpages%5Cstudent-dashboard.tsx&page=%2Fstudent-dashboard!", "(pages-dir-browser)/./components/TpPill.tsx", "(pages-dir-browser)/./hooks/useMastery.ts", "(pages-dir-browser)/./pages/DifficultyModal.tsx", "(pages-dir-browser)/./pages/student-dashboard.tsx", "(pages-dir-browser)/__barrel_optimize__?names=BookIcon,ClipboardCheckIcon,FlameIcon,GemIcon,HeartIcon,HomeIcon,TrophyIcon,UserIcon!=!../../node_modules/lucide-react/dist/esm/lucide-react.js", "(pages-dir-browser)/__barrel_optimize__?names=XIcon!=!../../node_modules/lucide-react/dist/esm/lucide-react.js"]}