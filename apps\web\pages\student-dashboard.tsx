import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import Head from 'next/head';
import { GemIcon, HeartIcon, FlameIcon, HomeIcon, BookIcon, TrophyIcon, UserIcon, ClipboardCheckIcon } from 'lucide-react';
import { TpPill } from '../components/TpPill';
import { useMastery } from '../hooks/useMastery';
import { DifficultyModal, DifficultyOption } from './DifficultyModal';

// Define types for our data structures
interface Subject {
  id: number;
  name: string;
  icon?: string;
  color?: string;
  completed: number;
  total: number;
  unlocked: boolean;
}

interface Unit {
  id: number;
  name: string;
  nameZh?: string;
  unitNumber?: number;
  tpLevel?: number;
  completed: boolean;
  unlocked: boolean;
}

interface Stats {
  streak: number;
  xp: number;
  gems: number;
  hearts: number;
}

interface Milestone {
  days: number;
  label: string;
  status: 'reached' | 'upcoming';
}

// Map subject names to icons and colors
const subjectIcons: Record<string, string> = {
  'Math': '🔢',
  'Science': '🧪',
  'Chinese': '🀄',
  'English': '📚',
  'Malay': '🇲🇾',
  'History': '📜',
  'Geography': '🌍',
  'Art': '🎨',
  'Music': '🎵',
  'Physical Education': '⚽',
};

const subjectColors: Record<string, string> = {
  'Math': 'bg-blue-500',
  'Science': 'bg-green-500',
  'Chinese': 'bg-red-500',
  'English': 'bg-purple-500',
  'Malay': 'bg-yellow-500',
  'History': 'bg-amber-500',
  'Geography': 'bg-emerald-500',
  'Art': 'bg-pink-500',
  'Music': 'bg-indigo-500',
  'Physical Education': 'bg-orange-500',
};

// This is v2 of the dashboard.
const StudentDashboard = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [showStreakModal, setShowStreakModal] = useState(false);
  const [selectedSubject, setSelectedSubject] = useState<number | null>(null);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [units, setUnits] = useState<Record<number, Unit[]>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [childData, setChildData] = useState<{ id: number; name: string; year: string; yearId: number; username: string } | null>(null);
  const [showDifficultyModal, setShowDifficultyModal] = useState(false);
  const [selectedUnit, setSelectedUnit] = useState<QuizUnit | null>(null);

  // Handle error messages from query parameters
  useEffect(() => {
    const { error: queryError } = router.query;
    if (queryError === 'unauthorized-quiz-access') {
      setError('You can only view your own quizzes. Please select a subject and start a new quiz.');

      // Clear the error from URL after displaying
      router.replace('/student-dashboard', undefined, { shallow: true });
    }
  }, [router.query]);

  const [stats, setStats] = useState<Stats>({
    streak: 5,
    xp: 230,
    gems: 45,
    hearts: 5,
  });

  useEffect(() => {
    // Redirect to login if not authenticated
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // Fetch child data
  useEffect(() => {
    if (status === 'authenticated' && session?.user) {
      setLoading(true);

      // Fetch the child's data
      fetch('/api/child-data')
        .then(res => {
          if (!res.ok) {
            throw new Error('Failed to fetch child data');
          }
          return res.json();
        })
        .then(data => {
          setChildData(data);

          // Now fetch subjects based on the child's year
          return fetch(`/api/subjects-by-year?yearNumber=${encodeURIComponent(data.year)}`);
        })
        .then(res => {
          if (!res.ok) {
            throw new Error('Failed to fetch subjects');
          }
          return res.json();
        })
        .then(data => {
          // Add icon and color to each subject
          const enhancedSubjects = data.map((subject: any) => ({
            ...subject,
            icon: subjectIcons[subject.name] || '📚',
            color: subjectColors[subject.name] || 'bg-gray-500',
            completed: 0, // This would come from the API in a real implementation
            total: subject.unitCount || 0,
            unlocked: true, // All subjects are unlocked for now
          }));

          setSubjects(enhancedSubjects);

          // Try to get the saved subject selection from localStorage
          try {
            const savedSubjectId = localStorage.getItem('selectedSubjectId');
            if (savedSubjectId !== null) {
              const parsedId = parseInt(savedSubjectId, 10);
              // Check if the saved subject exists in the fetched subjects
              if (!isNaN(parsedId) && enhancedSubjects.some((subject: Subject) => subject.id === parsedId)) {
                setSelectedSubject(parsedId);
              } else {
                // If saved subject doesn't exist in current subjects, default to first one
                if (enhancedSubjects.length > 0) {
                  setSelectedSubject(enhancedSubjects[0].id);
                }
              }
            } else {
              // No saved selection, default to first subject
              if (enhancedSubjects.length > 0) {
                setSelectedSubject(enhancedSubjects[0].id);
              }
            }
          } catch (error) {
            console.error('Error accessing localStorage:', error);
            // Fall back to default behavior
            if (enhancedSubjects.length > 0) {
              setSelectedSubject(enhancedSubjects[0].id);
            }
          }

          setLoading(false);
        })
        .catch(error => {
          console.error('Error fetching data:', error);
          setError('Failed to load data. Please try again later.');
          setLoading(false);
        });
    }
  }, [status, session]);

  // Save selected subject to localStorage when it changes
  useEffect(() => {
    if (selectedSubject !== null) {
      try {
        localStorage.setItem('selectedSubjectId', selectedSubject.toString());
      } catch (error) {
        console.error('Error saving to localStorage:', error);
      }
    }
  }, [selectedSubject]);

  // Fetch units when a subject is selected
  useEffect(() => {
    if (selectedSubject !== null && childData) {
      setLoading(true);

      fetch(`/api/units-by-subject-year?subjectId=${selectedSubject}&yearNumber=${encodeURIComponent(childData.year)}`)
        .then(res => {
          if (!res.ok) {
            throw new Error('Failed to fetch units');
          }
          return res.json();
        })
        .then(data => {
          setUnits(prevUnits => ({
            ...prevUnits,
            [selectedSubject]: data
          }));
          setLoading(false);
        })
        .catch(error => {
          console.error('Error fetching units:', error);
          setError('Failed to load units. Please try again later.');
          setLoading(false);
        });
    }
  }, [selectedSubject, childData]);

  // Create a component to handle mastery data for a single unit
  const UnitMasteryPill = ({ unitId, studentId }: { unitId: number; studentId: number }) => {
    const masteryData = useMastery(studentId, unitId);
    return (
      <TpPill
        tp={masteryData?.currentTp ?? 0}
        confidence={masteryData?.confidence ?? 'low'}
      />
    );
  };

  interface QuizUnit {
    id: number;
    name: string;
    subject: string;
  }

  const handleStartQuiz = (unit: QuizUnit) => {
    // Show the difficulty modal and store the selected unit
    setSelectedUnit(unit);
    setShowDifficultyModal(true);
  };

  const handleDifficultySelect = async (difficulty: DifficultyOption) => {
    // Close the modal
    setShowDifficultyModal(false);

    if (!selectedUnit || !childData) return;

    try {
      setLoading(true);

      // Get the year ID from the API if not available in childData
      let yearId = childData.yearId;

      if (!yearId) {
        try {
          // Extract year number from the year string (e.g., "Year 5" -> 5)
          const yearMatch = childData.year.match(/\d+/);
          const yearNumber = yearMatch ? parseInt(yearMatch[0], 10) : null;

          if (yearNumber) {
            // Fetch the year ID based on the year number
            const yearResponse = await fetch(`/api/years?yearNumber=${yearNumber}`);
            if (yearResponse.ok) {
              const yearData = await yearResponse.json();
              if (yearData && yearData.id) {
                yearId = yearData.id;
              }
            }
          }
        } catch (error) {
          console.error('Error fetching year ID:', error);
        }
      }

      if (!yearId) {
        setError('Could not determine year ID. Please try again or contact support.');
        setLoading(false);
        return;
      }

      // Create a practice quiz attempt using the new API
      const response = await fetch('/api/quiz/create-practice', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subjectId: selectedSubject,
          yearId: yearId,
          unitId: selectedUnit.id,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create practice quiz');
      }

      const data = await response.json();

      // Check if we should use v2 quiz
      const quizVersion = process.env.NEXT_PUBLIC_QUIZ_VERSION || 'v1';

      // Very visible debugging
      alert(`DEBUG: Quiz Version = ${quizVersion}, Env Var = ${process.env.NEXT_PUBLIC_QUIZ_VERSION}`);
      console.log("Quiz version from env:", quizVersion);
      console.log("All env vars:", {
        NEXT_PUBLIC_QUIZ_VERSION: process.env.NEXT_PUBLIC_QUIZ_VERSION,
        NEXT_PUBLIC_FEATURE_ADAPTIVE_V2: process.env.NEXT_PUBLIC_FEATURE_ADAPTIVE_V2
      });

      if (quizVersion === 'v2') {
        console.log("Redirecting to V2 quiz:", `/quiz/v2/${data.attemptId}`);
        router.push(`/quiz/v2/${data.attemptId}`);
      } else {
        console.log("Redirecting to V1 quiz:", `/quiz?attemptId=${data.attemptId}`);
        router.push(`/quiz?attemptId=${data.attemptId}`);
      }
    } catch (error) {
      console.error('Error creating practice quiz:', error);
      setError('Failed to start practice quiz. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDifficultyModalClose = () => {
    setShowDifficultyModal(false);
    setSelectedUnit(null);
  };

  const handleShowStreak = () => {
    setShowStreakModal(true);
  };

  const handleTestClick = () => {
    // Navigate to the test page or show test options
    router.push('/start-quiz');
  };

  // Show loading state while checking authentication
  if (status === 'loading' || status === 'unauthenticated') {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Student Dashboard | Studu</title>
        <meta name="description" content="Student dashboard" />
      </Head>

      <div className="flex flex-col w-full min-h-screen bg-white">
        {/* Header */}
        <header className="sticky top-0 w-full bg-[#0F5FA6] text-white p-3 flex items-center justify-between shadow-md">
          <div className="flex items-center">
            <div className="text-xl font-bold mr-2">Studu</div>
            {childData && (
              <div className="text-sm bg-[#0A8CBF] px-3 py-1 rounded-full">
                {childData.name} | {childData.year}
              </div>
            )}
          </div>
          <div className="flex items-center space-x-4">
            <button
              onClick={handleShowStreak}
              className="flex items-center bg-[#0A8CBF] rounded-full px-3 py-1"
            >
              <FlameIcon size={20} className="text-[#05DBF2] mr-1" />
              <span className="font-bold">{stats.streak}</span>
            </button>
            <div className="flex items-center bg-[#0A8CBF] rounded-full px-3 py-1">
              <GemIcon size={20} className="text-[#05DBF2] mr-1" />
              <span className="font-bold">{stats.gems}</span>
            </div>
            <div className="flex items-center bg-[#0A8CBF] rounded-full px-3 py-1">
              <HeartIcon size={20} className="text-red-500 mr-1" />
              <span className="font-bold">{stats.hearts}</span>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 p-4 bg-gray-100 overflow-y-auto">
          {/* Subjects Section */}
          <div className="mb-6">
            <h2 className="text-xl font-bold mb-3">Subjects</h2>
            {loading && subjects.length === 0 ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : error && subjects.length === 0 ? (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                <p>{error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="mt-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded text-sm"
                >
                  Retry
                </button>
              </div>
            ) : subjects.length === 0 ? (
              <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
                <p>No subjects found for your year. Please contact your teacher.</p>
              </div>
            ) : (
              <div className="flex overflow-x-auto pb-2 space-x-3">
                {subjects.map(subject => (
                  <button
                    key={subject.id}
                    onClick={() => setSelectedSubject(subject.id)}
                    className={`flex flex-col items-center justify-center p-4 rounded-lg shadow-md min-w-[100px] h-[100px] ${
                      selectedSubject === subject.id ? 'ring-4 ring-yellow-400' : ''
                    } ${subject.unlocked ? subject.color : 'bg-gray-400'}`}
                  >
                    <div className="text-3xl mb-1">{subject.icon}</div>
                    <div className="text-white font-bold">{subject.name}</div>
                    <div className="text-xs text-white/80 mt-1">
                      {subject.completed}/{subject.total}
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Learning Path Section */}
          {selectedSubject !== null && (
            <div>
              <h2 className="text-xl font-bold mb-3">Learning Path</h2>
              <div className="bg-white rounded-xl p-4 shadow-md">
                <div className="flex items-center mb-4">
                  <div className="text-3xl mr-3">
                    {subjects.find(s => s.id === selectedSubject)?.icon}
                  </div>
                  <div>
                    <h3 className="font-bold text-lg">
                      {subjects.find(s => s.id === selectedSubject)?.name}
                    </h3>
                    <div className="text-sm text-gray-500">
                      Complete lessons to unlock new content
                    </div>
                  </div>
                </div>

                {loading && (!units[selectedSubject] || units[selectedSubject].length === 0) ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
                  </div>
                ) : error && (!units[selectedSubject] || units[selectedSubject].length === 0) ? (
                  <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    <p>{error}</p>
                  </div>
                ) : !units[selectedSubject] || units[selectedSubject].length === 0 ? (
                  <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
                    <p>No units found for this subject. Please contact your teacher.</p>
                  </div>
                ) : (
                  <div className="flex flex-col space-y-4">
                    {units[selectedSubject].map((unit, index) => (
                      <div
                        key={unit.id}
                        className={`relative border-2 rounded-lg p-4 hover:shadow-md transition-shadow ${
                          unit.unlocked
                            ? 'border-[#0A8CBF] bg-gradient-to-r from-[#04B2D9]/5 to-white'
                            : 'border-gray-300 bg-gray-100'
                        }`}
                      >
                        <div className="flex justify-between items-center">
                          <div>
                            <h4 className="font-bold flex items-center gap-2">
                              Unit {unit.unitNumber || index + 1}: {unit.name}
                              {process.env.NEXT_PUBLIC_FEATURE_ADAPTIVE_V2 === 'on' && childData && (
                                <UnitMasteryPill
                                  unitId={unit.id}
                                  studentId={childData.id}
                                />
                              )}
                            </h4>
                            {unit.nameZh && (
                              <h5 className="text-sm text-gray-600 mb-1">
                                {unit.nameZh}
                              </h5>
                            )}
                            {/* Removed TP level badge */}
                          </div>
                          {unit.unlocked ? (
                            <button
                              onClick={() => handleStartQuiz({
                                id: unit.id,
                                name: unit.name,
                                subject: subjects.find(s => s.id === selectedSubject)?.name || ''
                              })}
                              className="px-4 py-2 rounded-lg font-bold bg-[#0F5FA6] text-white"
                            >
                              Practice
                            </button>
                          ) : (
                            <div className="bg-gray-300 p-2 rounded-full">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="text-gray-600"
                              >
                                <rect width="18" height="11" x="3" y="11" rx="2" ry="2" />
                                <path d="M7 11V7a5 5 0 0 1 10 0v4" />
                              </svg>
                            </div>
                          )}
                        </div>
                        {/* Removed progress bar */}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </main>

        {/* Footer */}
        <footer className="sticky bottom-0 w-full bg-white border-t border-gray-200 py-2">
          <div className="flex justify-around items-center">
            <button className="flex flex-col items-center p-2 text-[#0F5FA6]">
              <HomeIcon size={24} />
              <span className="text-xs mt-1">Home</span>
            </button>
            <button className="flex flex-col items-center p-2 text-gray-500 hover:text-[#0A8CBF]">
              <BookIcon size={24} />
              <span className="text-xs mt-1">Lessons</span>
            </button>
            <button className="flex flex-col items-center p-2 text-gray-500 hover:text-[#0A8CBF]">
              <TrophyIcon size={24} />
              <span className="text-xs mt-1">Achievements</span>
            </button>
            <button className="flex flex-col items-center p-2 text-gray-500 hover:text-[#0A8CBF]">
              <UserIcon size={24} />
              <span className="text-xs mt-1">Profile</span>
            </button>
            <button
              onClick={handleTestClick}
              className="flex flex-col items-center p-2 text-gray-500 hover:text-[#0A8CBF]"
            >
              <ClipboardCheckIcon size={24} />
              <span className="text-xs mt-1">Test</span>
            </button>
          </div>
        </footer>

        {/* Streak Modal */}
        {showStreakModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-xl max-w-md w-full p-6 shadow-xl">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold flex items-center">
                  <FlameIcon size={28} className="text-[#0F5FA6] mr-2" />
                  Your Streak
                </h2>
                <button onClick={() => setShowStreakModal(false)} className="p-1">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M18 6 6 18" />
                    <path d="m6 6 12 12" />
                  </svg>
                </button>
              </div>
              <div className="text-center mb-6">
                <div className="text-6xl font-bold text-[#0F5FA6] mb-2">{stats.streak}</div>
                <div className="text-gray-600">
                  {stats.streak === 1 ? 'day' : 'days'} in a row
                </div>
              </div>
              <div className="space-y-4 mb-6">
                {[
                  { days: 7, label: '1 Week', status: stats.streak >= 7 ? 'reached' : 'upcoming' },
                  { days: 14, label: '2 Weeks', status: stats.streak >= 14 ? 'reached' : 'upcoming' },
                  { days: 30, label: '1 Month', status: stats.streak >= 30 ? 'reached' : 'upcoming' },
                  { days: 100, label: '100 Days', status: stats.streak >= 100 ? 'reached' : 'upcoming' }
                ].map(milestone => (
                  <div
                    key={milestone.days}
                    className={`flex justify-between items-center p-4 rounded-lg ${
                      milestone.status === 'reached' ? 'bg-[#04B2D9]/10' : 'bg-gray-100'
                    }`}
                  >
                    <div className="flex items-center">
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                          milestone.status === 'reached' ? 'bg-[#0F5FA6] text-white' : 'bg-gray-300'
                        }`}
                      >
                        {milestone.status === 'reached' ? '✓' : ''}
                      </div>
                      <span className="font-medium">{milestone.label}</span>
                    </div>
                    <div className="font-bold">{milestone.days} days</div>
                  </div>
                ))}
              </div>
              <div className="text-center text-gray-600 text-sm">
                Keep learning daily to build your streak!
              </div>
            </div>
          </div>
        )}

        {/* Difficulty Selection Modal */}
        {showDifficultyModal && selectedUnit && (
          <DifficultyModal
            onSelect={handleDifficultySelect}
            onClose={handleDifficultyModalClose}
            unit={selectedUnit}
          />
        )}
      </div>
    </>
  );
};

export default StudentDashboard;
